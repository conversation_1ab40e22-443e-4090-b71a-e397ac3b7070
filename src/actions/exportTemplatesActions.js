import { getUrlSearchParamsString } from "@develop/fe-library/dist/utils"

import api from "api"

import { customerIdSelector } from "selectors/mainStateSelectors"

import getMainApp from "components/MainAppProvider"

import generateActions from "utils/generateActions"

const actionPrefix = "exportTemplates"

const {
  exportTemplatesService: {
    addExportTemplate,
    deleteExportTemplate,
    getExportTemplates,
    getExportTemplateById,
    updateExportTemplate,
    getExportTemplateFiltersFields,
    getExportTemplateOrderFiltersFields,
    getExportTemplateFieldGroup,
  },
} = api

export const types = {
  setIsExportTemplateDrawerVisible: `${actionPrefix}/set_is_export_template_drawer_visible`,
  resetCurrentTemplate: `${actionPrefix}/reset_current_template_options`,
  changeSearchOptions: `${actionPrefix}/change_search_options`,
  getExportTemplates: [
    `${actionPrefix}/get_request`,
    `${actionPrefix}/get_success`,
    `${actionPrefix}/get_failure`,
  ],
  addExportTemplate: [
    `${actionPrefix}/add_export_template_request`,
    `${actionPrefix}/add_export_template_success`,
    `${actionPrefix}/add_export_template_failure`,
  ],
  deleteExportTemplate: [
    `${actionPrefix}/delete_item_request`,
    `${actionPrefix}/delete_item_success`,
    `${actionPrefix}/delete_item_failure`,
  ],
  getAllExportTemplates: [
    `${actionPrefix}/get_all_request`,
    `${actionPrefix}/get_all_success`,
    `${actionPrefix}/get_all_failure`,
  ],
  getExportTemplateById: [
    `${actionPrefix}/get_by_id_request`,
    `${actionPrefix}/get_by_id_success`,
    `${actionPrefix}/get_by_id_failure`,
  ],
  getFieldGroups: [
    `${actionPrefix}/get_field_groups_request`,
    `${actionPrefix}/get_field_groups_success`,
    `${actionPrefix}/get_field_groups_failure`,
  ],
  updateExportTemplate: [
    `${actionPrefix}/update_request`,
    `${actionPrefix}/update_success`,
    `${actionPrefix}/update_failure`,
  ],
  getExportTemplateFiltersFields: [
    `${actionPrefix}/get_template_filters_request`,
    `${actionPrefix}/get_template_filters_success`,
    `${actionPrefix}/get_template_filters_failure`,
  ],
  getExportTemplateOrderFiltersFields: [
    `${actionPrefix}/get_template_order_filters_request`,
    `${actionPrefix}/get_template_order_filters_success`,
    `${actionPrefix}/get_template_order_filters_failure`,
  ],
  getExportTemplateFieldGroup: [
    `${actionPrefix}/get_template_field_group`,
    `${actionPrefix}/get_template_field_group_success`,
    `${actionPrefix}/get_template_field_group_failure`,
  ],
}

let actions

const definitions = {
  setIsExportTemplateDrawerVisible: (props) => props,
  getExportTemplates: (searchOptions = {}, dispatch, getState) => ({
    callApi: () => {
      const state = getState()
      const customerId = customerIdSelector(state)

      dispatch({
        type: types.changeSearchOptions,
        payload: searchOptions,
      })

      const { actions: mainAppActions } = getMainApp()

      mainAppActions?.pushUrl?.(
        getUrlSearchParamsString({ params: searchOptions }),
      )

      const { title, ...restSearchOptions } = searchOptions

      return getExportTemplates({
        customerId,
        ...restSearchOptions,
        title: title ? encodeURIComponent(title) : null,
      })
    },
  }),
  getExportTemplateById: ({ id }, successCallback, dispatch, getState) => ({
    callApi: () => {
      const state = getState()
      const customerId = customerIdSelector(state)

      return getExportTemplateById({ id, customerId })
    },
    successCallback,
  }),
  deleteExportTemplate: (
    { id },
    successCallback,
    failureCallback,
    dispatch,
    getState,
  ) => ({
    callApi: () => {
      const state = getState()
      const customerId = customerIdSelector(state)

      return deleteExportTemplate({ id, customerId })
    },
    successCallback,
    failureCallback,
    doNotShowError: true,
  }),
  addExportTemplate: (
    { title: templateTitle, ...payload },
    successCallback,
    failureCallback,
    dispatch,
    getState,
  ) => ({
    callApi: () => {
      const state = getState()
      const customerId = customerIdSelector(state)
      const title = templateTitle ? encodeURIComponent(templateTitle) : null

      return addExportTemplate({
        customerId,
        title,
        ...payload,
      })
    },
    successCallback,
    failureCallback,
  }),
  changeSearchOptions: (searchOptions) => searchOptions,
  getAllExportTemplates: (searchOptions = {}, dispatch, getState) => ({
    callApi: () => {
      const state = getState()
      const customerId = customerIdSelector(state)

      return getExportTemplates({
        customerId,
        all: 1,
        ...searchOptions,
      })
    },
  }),
  updateExportTemplate: (
    { id, values: { title, ...formValues } },
    successCallback,
    failureCallback,
    dispatch,
    getState,
  ) => ({
    callApi: () => {
      const state = getState()
      const customerId = customerIdSelector(state)

      return updateExportTemplate({
        id,
        customerId,
        title: encodeURIComponent(title),
        ...formValues,
      })
    },
    successCallback,
    failureCallback,
  }),
  getExportTemplateFiltersFields: (
    { handler_name, successCallback, failureCallback },
    dispatch,
    getState,
  ) => ({
    callApi: () => {
      const state = getState()
      const customerId = customerIdSelector(state)

      return getExportTemplateFiltersFields({
        customerId,
        handler_name,
      })
    },
    successCallback,
    failureCallback,
  }),
  getExportTemplateOrderFiltersFields: (
    {
      fields,
      handler_name,
      successCallback = undefined,
      failureCallback = undefined,
    },
    dispatch,
    getState,
  ) => ({
    callApi: () => {
      const state = getState()
      const customerId = customerIdSelector(state)

      return getExportTemplateOrderFiltersFields({
        customerId,
        fields,
        handler_name,
      })
    },
    successCallback,
    failureCallback,
  }),
  resetCurrentTemplate: () => {},
  getExportTemplateFieldGroup: (
    { handler_name, successCallback = undefined, failureCallback = undefined },
    dispatch,
    getState,
  ) => ({
    callApi: () => {
      const state = getState()
      const customerId = customerIdSelector(state)

      return getExportTemplateFieldGroup({
        customerId,
        handler_name,
      })
    },
    successCallback,
    failureCallback,
  }),
}

actions = generateActions(definitions, types)

export default actions
