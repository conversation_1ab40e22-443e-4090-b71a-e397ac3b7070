import {
  ActionAbstractDefinitionType,
  ActionAsyncDefinitionType,
  ActionsTypes,
} from "actions/types"

export type OperationalDashboardActionsAsyncTypeNames =
  | "getDataCompletenessFactors"
  | "getDataCompletenessReferralFeeChanges"
  | "getDataCompletenessFbaFulfillmentFeeChanges"
  | "getDataCompletenessAdjustmentToFees"

export type OperationalDashboardActionsSyncTypeNames =
  | "openDataReassemblyModal"
  | "openHistoricalDataLoadModal"
  | "openReferralFeeChangesDrawer"
  | "openFbaFulfillmentFeeChangesDrawer"
  | "openAdjustmentToFeesDrawer"
  | "setAdditionalDataModal"
  | "clearOperationalDashboardData"
  | "clearOperationalDashboardDrawersData"

export type OperationalDashboardTypes = ActionsTypes<
  OperationalDashboardActionsAsyncTypeNames,
  OperationalDashboardActionsSyncTypeNames
>

export type OperationalDashboardDefinitions = {
  getDataCompletenessFactors: ActionAsyncDefinitionType<any>
  getDataCompletenessReferralFeeChanges: ActionAsyncDefinitionType<any>
  getDataCompletenessFbaFulfillmentFeeChanges: ActionAsyncDefinitionType<any>
  getDataCompletenessAdjustmentToFees: ActionAsyncDefinitionType<any>
  openDataReassemblyModal: ActionAbstractDefinitionType<any>
  openHistoricalDataLoadModal: ActionAbstractDefinitionType<any>
  openReferralFeeChangesDrawer: ActionAbstractDefinitionType<any>
  openFbaFulfillmentFeeChangesDrawer: ActionAbstractDefinitionType<any>
  openAdjustmentToFeesDrawer: ActionAbstractDefinitionType<any>
  setAdditionalDataModal: ActionAbstractDefinitionType<any>
  clearOperationalDashboardData: ActionAbstractDefinitionType<any>
  clearOperationalDashboardDrawersData: ActionAbstractDefinitionType<any>
}
