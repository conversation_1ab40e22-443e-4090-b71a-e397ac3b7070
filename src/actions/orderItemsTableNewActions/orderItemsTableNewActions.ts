import api from "api"

import { OrderItemsTableInitialData } from "initialState/orderItemsTable"

import { withActionPrefix } from "actions/utils"

import { customerIdSelector } from "selectors/mainStateSelectors"

import generateActions from "utils/generateActions"
import {
  buildTableSettingsActionDefinitions,
  buildTableSettingsActionTypes,
} from "utils/tableSettingsHelpers"

import { ORDER_ITEMS_TABLE_NEW_SETTINGS_KEY } from "constants/orderItemsTableNew"

import { GridGeneral } from "types"
import { AmazonOrderExtendedViewItemV1 } from "types/Models/AmazonOrderExtendedViewItem"

import {
  OrderItemsTableNewDefinitions,
  OrderItemsTableNewTypes,
} from "./orderItemsTableNewActionsTypes"

const {
  ordersService: { getOrders },
} = api

export const PREFIX = "orderItemsTableNew"

const createActionType = withActionPrefix(PREFIX)

// Use of ActionsTypes causes a strange error when not including sync types.
// TODO: check and fix.
// @ts-expect-error
export const types: OrderItemsTableNewTypes = {
  getAmazonOrders: createActionType("getAmazonOrders", true),
  clear: createActionType("clear"),
  ...buildTableSettingsActionTypes(PREFIX),
}

const definitions: OrderItemsTableNewDefinitions = {
  getAmazonOrders: (
    { params = {}, successCallback = () => {}, failureCallback = () => {} },
    dispatch,
    getState,
  ) => ({
    callApi: async (): Promise<GridGeneral<AmazonOrderExtendedViewItemV1[]>> => {
      const state = getState()
      const customerId = customerIdSelector(state)

      const response = await getOrders({
        ...params,
        customerId,
      })

      return (
        Array.isArray(response?.data?.data)
          ? response
          : { data: OrderItemsTableInitialData }
      ) as GridGeneral<AmazonOrderExtendedViewItemV1[]>
    },
    successCallback,
    failureCallback,
  }),

  clear: () => {},
  ...buildTableSettingsActionDefinitions({
    storeKey: PREFIX,
    tableSettingsKey: ORDER_ITEMS_TABLE_NEW_SETTINGS_KEY,
  }),
}

const orderItemsTableNewActions = generateActions(
  types,
  definitions,
) as OrderItemsTableNewDefinitions

export { orderItemsTableNewActions }
