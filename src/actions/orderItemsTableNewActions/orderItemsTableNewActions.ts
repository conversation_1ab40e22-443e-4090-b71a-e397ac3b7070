import api from "api"

import { OrderItemsTableInitialData } from "initialState/orderItemsTable"

import { withActionPrefix } from "actions/utils"

import { customerIdSelector } from "selectors/mainStateSelectors"

import generateActions from "utils/generateActions"
import {
  buildTableSettingsActionDefinitions,
  buildTableSettingsActionTypes,
} from "utils/tableSettingsHelpers"

import { ORDER_ITEMS_TABLE_NEW_SETTINGS_KEY } from "constants/orderItemsTableNew"

import { GridGeneral } from "types"
import { AmazonOrderExtendedViewItemV1 } from "types/Models/AmazonOrderExtendedViewItem"

import {
  OrderItemsTableNewDefinitions,
  OrderItemsTableNewTypes,
} from "./orderItemsTableNewActionsTypes"

const { getOrders, getOrderStatuses } = api

export const PREFIX = "orderItemsTableNew"

const createActionType = withActionPrefix(PREFIX)

export const types: OrderItemsTableNewTypes = {
  getAmazonOrders: createActionType("getAmazonOrders", true),
  getAmazonOrderStatuses: createActionType("getAmazonOrderStatuses", true),
  clear: createActionType("clear"),
  ...buildTableSettingsActionTypes({
    storeKey: PREFIX,
  }),
}

let orderItemsTableNewActions: OrderItemsTableNewDefinitions

const definitions: OrderItemsTableNewDefinitions = {
  getAmazonOrders: (
    { params = {}, successCallback = () => {}, failureCallback = () => {} },
    dispatch,
    getState,
  ) => ({
    callApi: async (): Promise<GridGeneral<AmazonOrderExtendedViewItemV1[]>> => {
      const state = getState()
      const customerId = customerIdSelector(state)

      const response = await getOrders({
        ...params,
        customerId,
      })

      return (
        Array.isArray(response?.data?.data)
          ? response
          : { data: OrderItemsTableInitialData }
      ) as GridGeneral<AmazonOrderExtendedViewItemV1[]>
    },
    successCallback,
    failureCallback,
  }),
  getAmazonOrderStatuses: (
    { params = {}, successCallback = () => {}, failureCallback = () => {} },
    dispatch,
    getState,
  ) => ({
    callApi: async () => {
      const state = getState()
      const customerId = customerIdSelector(state)

      return await getOrderStatuses({
        ...params,
        customerId,
      })
    },
    successCallback,
    failureCallback,
  }),
  clear: () => {},
  ...buildTableSettingsActionDefinitions({
    storeKey: PREFIX,
    tableSettingsKey: ORDER_ITEMS_TABLE_NEW_SETTINGS_KEY,
  }),
}

orderItemsTableNewActions = generateActions<
  OrderItemsTableNewTypes,
  OrderItemsTableNewDefinitions
>(types, definitions)

export { orderItemsTableNewActions }
