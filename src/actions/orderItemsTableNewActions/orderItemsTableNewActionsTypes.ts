import type { ActionAsyncDefinitionType, ActionsTypes } from "actions/types"

import type { AmazonOrderRequestType } from "types"
import type {
  TableSettingsActionDefinitions,
  TableSettingsActionTypes,
} from "types/TableSettings"

export type OrderItemsTableNewActionsAsyncTypeNames =
  | "getAmazonOrders"

export type OrderItemsTableNewActionsSyncTypeNames = "clear"

export type OrderItemsTableNewTypes = ActionsTypes<
  OrderItemsTableNewActionsAsyncTypeNames,
  OrderItemsTableNewActionsSyncTypeNames
> &
  TableSettingsActionTypes

export type OrderItemsTableNewDefinitions = {
  getAmazonOrders: ActionAsyncDefinitionType<AmazonOrderRequestType>
  clear: () => void
} & TableSettingsActionDefinitions
