import api from "api"

import { withActionPrefix } from "actions/utils"

import { customerIdSelector } from "selectors/mainStateSelectors"
import { marketplaceGroupsSelector } from "selectors/marketplaceSelectors"
import {
  getCurrentOrderCurrencyIdSelector,
  getCurrentOrderIdSelector,
  getCurrentOrderItemSelector,
} from "selectors/ordersSelectors"

import getMainApp from "components/MainAppProvider"

import { formatSellerIds } from "utils/formatSellerIdsQuery"
import generateActions from "utils/generateActions"
import { getFormattedRequestDateRange } from "utils/transactions"

import { OrdersActionTypes, OrdersDefinitions } from "./ordersActionsTypes"
import { AmazonOrderAmountCostResponseData } from "api/ordersService/ordersServiceTypes"

const actionPrefix = "orders"
const createActionType = withActionPrefix(actionPrefix)

const {
  ordersService: {
    getOrders,
    getOrderStatuses,
    getLastUpdateOrdersDate,
    getAmazonOrderFeesBreakdown,
    getAmazonOrderExpensesBreakdown,
    getAmazonOrderDetails,
    getAmazonOrderAmountCosts,
    updateAmazonOrderAmountCosts,
  },
} = api

const toggleSpinner = (isVisible: boolean): void => {
  const mainApp = getMainApp()

  mainApp?.actions?.showSpiner?.(null, isVisible, 1000000)
}

const types: OrdersActionTypes = {
  displayModal: createActionType("display_modal"),
  updateUrlParams: createActionType("update_url_params"),
  changeSearchOptions: createActionType("change_search_options"),
  getOrders: createActionType("get_orders", true),
  getOrderStatuses: createActionType("get_orders_statuses", true),
  getLastUpdateOrdersDate: createActionType("get_last_update_date", true),
  getAmazonOrderFeesBreakdown: createActionType("get_order_fees", true),
  getAmazonOrderExpensesBreakdown: createActionType("get_order_expenses", true),
  getAmazonOrderDetails: createActionType("get_order_details", true),
  getAmazonOrderAmountCosts: createActionType("get_order_amount_costs", true),
  updateAmazonOrderAmountCosts: createActionType(
    "update_order_amount_costs",
    true,
  ),
} as const

const definitions: OrdersDefinitions = {
  displayModal: (props) => props,
  updateUrlParams: (props) => props,
  changeSearchOptions: (props) => props,
  getLastUpdateOrdersDate: (props, dispatch, getState) => ({
    callApi: () => {
      const state = getState?.()
      const customerId = customerIdSelector(state)

      return getLastUpdateOrdersDate({
        customerId,
      })
    },
  }),
  getOrderStatuses: (params, dispatch, getState) => ({
    callApi: () => {
      const state = getState?.()
      const customerId = customerIdSelector(state)

      return getOrderStatuses({
        customerId,
      })
    },
  }),
  getOrders: ({ params, successCallback }, dispatch, getState) => ({
    callApi: () => {
      const state = getState?.()
      const customerId = customerIdSelector(state)
      const groups = marketplaceGroupsSelector(state)

      const { searchOptions = {} } = params || {}

      const {
        from,
        to,
        groupId,
        sellerId,
        marketplaces,
        marketplace_id,
        order_purchase_date,
        ...requestOptions
      } = searchOptions

      dispatch?.(ordersActions.changeSearchOptions(searchOptions) as any)

      const isSellerOrGroupFromUrl = groupId || sellerId

      const orderPurchaseDate: string | undefined =
        getFormattedRequestDateRange({
          // @ts-expect-error
          rangeDateString: order_purchase_date,
          // @ts-expect-error
          dateFrom: from,
          // @ts-expect-error
          dateTo: to,
          convertToUTC: true,
        }) ?? undefined

      const marketplaceSellerIds = JSON.stringify(
        formatSellerIds(searchOptions, groups),
      )

      const marketplacesGroupList = isSellerOrGroupFromUrl
        ? { marketplace_seller_ids: marketplaceSellerIds }
        : {}

      const marketplacesList = marketplace_id || marketplaces

      return getOrders({
        customerId,
        ...requestOptions,
        order_purchase_date: orderPurchaseDate,
        ...marketplacesGroupList,
        marketplace_id: marketplacesList,
      })
    },
    successCallback,
  }),
  getAmazonOrderFeesBreakdown: (props, dispatch, getState) => ({
    callApi: () => {
      const state = getState?.()

      if (!state) {
        return
      }

      const customerId = customerIdSelector(state)
      const currentOrder = getCurrentOrderItemSelector(state)

      if (!currentOrder) {
        return
      }

      const { order_item_id: amazonOrderItemId, currency_id: currencyId } =
        currentOrder

      return getAmazonOrderFeesBreakdown({
        customerId,
        amazonOrderItemId,
        currencyId,
      })
    },
  }),
  getAmazonOrderExpensesBreakdown: (props, dispatch, getState) => ({
    callApi: () => {
      const state = getState?.()

      if (!state) {
        return
      }

      const customerId = customerIdSelector(state)
      const currentOrder = getCurrentOrderItemSelector(state)

      if (!currentOrder) {
        return
      }

      const { order_item_id: amazonOrderItemId, currency_id: currencyId } =
        currentOrder

      return getAmazonOrderExpensesBreakdown({
        customerId,
        amazonOrderItemId,
        currencyId,
      })
    },
  }),
  getAmazonOrderDetails: (props, dispatch, getState) => ({
    callApi: () => {
      const state = getState?.()

      if (!state) {
        return
      }

      const customerId = customerIdSelector(state)
      const amazonOrderId = getCurrentOrderIdSelector(state)
      const currencyId = getCurrentOrderCurrencyIdSelector(state)

      if (!amazonOrderId || !currencyId) {
        return
      }

      return getAmazonOrderDetails({
        customerId,
        amazonOrderId,
        currencyId,
      })
    },
  }),
  getAmazonOrderAmountCosts: (
    { params, successCallback },
    dispatch,
    getState,
  ) => ({
    callApi: async (): Promise<
      AmazonOrderAmountCostResponseData | undefined
    > => {
      if (!params?.amazonOrderId) {
        return
      }

      const state = getState?.()
      const customerId = customerIdSelector(state)

      const amazonOrderAmountCosts = await getAmazonOrderAmountCosts({
        customerId,
        amazonOrderId: params.amazonOrderId,
      })

      return amazonOrderAmountCosts.data
    },
    successCallback,
  }),
  updateAmazonOrderAmountCosts: (
    { params, successCallback, failureCallback },
    dispatch,
    getState,
  ) => ({
    callApi: async () => {
      if (!params?.amazonOrderId || !params?.payload) {
        return {}
      }

      const state = getState?.()
      const customerId = customerIdSelector(state)

      toggleSpinner(true)

      const result = await updateAmazonOrderAmountCosts({
        params: { customerId, amazonOrderId: params.amazonOrderId },
        payload: params.payload,
      })

      if (result?.data?.result === "successful") {
        return params.payload
      }

      return {}
    },
    successCallback: (params: any): void => {
      toggleSpinner(false)
      successCallback?.(params)
    },
    failureCallback: (params: any): void => {
      toggleSpinner(false)
      failureCallback?.(params)
    },
  }),
} as const

const ordersActions = generateActions(definitions, types) as OrdersDefinitions

export { definitions, ordersActions, types }
