import type { ActionAsyncDefinitionType, ActionsTypes } from "actions/types"

import type { GetProductAggregatedSalesInfosRequestParams } from "types/RequestParams/GetProductAggregatedSalesInfosRequestParams"
import type {
  TableSettingsActionDefinitions,
  TableSettingsActionTypes,
} from "types/TableSettings"

export type ProductAggregatedSalesInfoActionsAsyncTypeNames =
  | "getProductAggregatedSalesInfo"

export type ProductAggregatedSalesInfoActionsSyncTypeNames = "clear"

export type ProductAggregatedSalesInfoTypes = ActionsTypes<
  ProductAggregatedSalesInfoActionsAsyncTypeNames,
  ProductAggregatedSalesInfoActionsSyncTypeNames
> &
  TableSettingsActionTypes

export type ProductAggregatedSalesInfoDefinitions = {
  getProductAggregatedSalesInfo: ActionAsyncDefinitionType<GetProductAggregatedSalesInfosRequestParams>
} & TableSettingsActionDefinitions
