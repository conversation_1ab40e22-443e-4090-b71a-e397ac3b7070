import {
  getUrlSearchParams,
  getUrlSearchParamsString,
} from "@develop/fe-library/dist/utils"

import generateActions from "utils/generateActions"

import getMainApp from "components/MainAppProvider"

export const types = {
  changeUrl: "gridSearch/change_url",
  init: "gridSearch/init",
  pageChange: "gridSearch/page_change",
}

const definitions = {
  changeUrl: (params, replace, dispatch) => {
    const urlParams = getUrlSearchParams({
      locationSearch: document.location.search,
    })

    let urlString = getUrlSearchParamsString({
      params: { ...urlParams, ...params },
    })

    if (replace === true) {
      urlString = getUrlSearchParamsString({
        params,
      })
    }

    const { actions: mainAppActions } = getMainApp()
    mainAppActions?.pushUrl?.(urlString)

    return {
      urlString: urlString.substring(1),
    }
  },
  init: () => ({
    queryParams: getUrlSearchParams({
      locationSearch: document.location.search,
    }),
  }),
}

export default generateActions(definitions, types)
