import api from "api"

import { customerIdSelector } from "selectors/mainStateSelectors"

import generateActions from "utils/generateActions"

const actionPrefix = "segments"

const {
  segmentsService: { createSegment, deleteSegment, getSegments, updateSegment },
} = api

export const types = {
  selectSidebarTabId: `${actionPrefix}/select_sidebar_tab_id`,

  selectSegmentToEdit: `${actionPrefix}/select_segment_to_edit`,
  clearSegmentToEdit: `${actionPrefix}/clear_segment_to_edit`,

  getSegments: [
    `${actionPrefix}/get_request`,
    `${actionPrefix}/get_success`,
    `${actionPrefix}/get_failure`,
  ],

  createSegment: [
    `${actionPrefix}/create_request`,
    `${actionPrefix}/create_success`,
    `${actionPrefix}/create_failure`,
  ],

  updateSegment: [
    `${actionPrefix}/update_request`,
    `${actionPrefix}/update_success`,
    `${actionPrefix}/update_failure`,
  ],

  deleteSegment: [
    `${actionPrefix}/delete_request`,
    `${actionPrefix}/delete_success`,
    `${actionPrefix}/delete_failure`,
  ],
}

const definitions = {
  selectSidebarTabId: (payload) => payload,

  selectSegmentToEdit: (payload) => payload,
  clearSegmentToEdit: () => {},

  getSegments: (successCallback, failureCallback, dispatch, getState) => ({
    callApi: async () => {
      const state = getState()
      const customerId = customerIdSelector(state)

      const response = await getSegments({ customerId })

      return response?.data || []
    },
    successCallback,
    failureCallback,
  }),

  createSegment: (
    payload,
    successCallback,
    failureCallback,
    dispatch,
    getState,
  ) => ({
    callApi: () => {
      const state = getState()
      const customerId = customerIdSelector(state)

      return createSegment({ customerId, ...payload })
    },
    successCallback,
    failureCallback,
  }),

  updateSegment: (
    id,
    payload,
    successCallback,
    failureCallback,
    dispatch,
    getState,
  ) => ({
    callApi: () => {
      const state = getState()
      const customerId = customerIdSelector(state)

      return updateSegment({ id, customerId, ...payload })
    },
    successCallback,
    failureCallback,
  }),

  deleteSegment: (
    id,
    successCallback,
    failureCallback,
    dispatch,
    getState,
  ) => ({
    callApi: () => {
      const state = getState()
      const customerId = customerIdSelector(state)

      return deleteSegment({ id, customerId })
    },
    successCallback,
    failureCallback,
  }),
}

let actions

actions = generateActions(definitions, types)

export default actions
