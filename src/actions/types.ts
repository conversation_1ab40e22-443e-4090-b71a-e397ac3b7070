import { Dispatch } from "redux"

import { RootState } from "types/store/store"

export type ActionTypeAsync = [
  `${string}_request`,
  `${string}_success`,
  `${string}_failure`,
]

export type ActionTypeSync = string

export type ActionType = ActionTypeAsync | ActionTypeSync

export type ActionsTypes<
  AsyncNames extends string = string,
  SyncN<PERSON>s extends string = string,
> = {
  [Key in AsyncNames]: ActionTypeAsync
} & {
  [Key in SyncNames]: ActionTypeSync
}

export type SuccessCallbackType = <Response extends unknown>(
  response: Response,
) => any

export type FailureCallbackType = <Error extends unknown>(error: Error) => any

export type ActionArgsType<Params> = {
  params?: Params
  successCallback?: SuccessCallbackType
  failureCallback?: FailureCallbackType
}

export type ActionAsyncDefinitionType<Params extends Record<string, any>> = (
  { params, successCallback, failureCallback }: ActionArgsType<Params>,
  dispatch?: Dispatch,
  getState?: () => RootState,
) => any

export type ActionAbstractDefinitionType<Args> = (params: Args) => Args
