import { getUrlSearchParamsString } from "@develop/fe-library/dist/utils"

import config from "config"

import { get } from "utils/request"

const { apiVersion } = config

export const getAmazonCustomerAccountsMarketPlaces = (params = {}) =>
  get(
    `/v${apiVersion}/amazon-customer-account-marketplace${getUrlSearchParamsString(
      { params },
    )}`,
  )

export default {
  getAmazonCustomerAccountsMarketPlaces,
}
