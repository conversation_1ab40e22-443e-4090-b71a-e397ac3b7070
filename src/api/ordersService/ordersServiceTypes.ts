import { OrderType } from "types"
import { OfferType } from "types/OfferType"
import {
  OrderAmountCost,
  OrderItem,
  ProfitBreakdownItem,
  Summary,
} from "types/OrderDetails"
import { OrderStatus } from "types/OrderStatus"

export type OrderDetailsResponse = {
  currency_id: string
  is_approximate_amounts_calculation: boolean
  updated_at: string
  summary: Summary
  breakdown: ProfitBreakdownItem[]
  products: OrderItem[]
}

export type ProfitBreakdownResponseData = {
  currency_id: string
  is_approximate_amounts_calculation: boolean
  order_id: string | null
  breakdown: ProfitBreakdownItem[]
}

export type GetAmazonOrderFeesBreakdownParams = {
  customerId: number
  amazonOrderItemId: string
  currencyId: string
}

export type GetAmazonOrderFeesBreakdownResponse = {
  fileName?: string
  blob?: Blob
  data?: ProfitBreakdownResponseData
}

export type GetAmazonOrderExpensesBreakdownParams =
  GetAmazonOrderFeesBreakdownParams

export type GetAmazonOrderExpensesBreakdownResponse =
  GetAmazonOrderFeesBreakdownResponse

export type GetAmazonOrderDetailsParams = {
  customerId?: number
  amazonOrderId: string
  currencyId: string
}

export type GetAmazonOrderDetailsResponse = {
  fileName?: string
  blob?: Blob
  data?: OrderDetailsResponse
}

export type GetAmazonOrderParams = {
  customerId?: number // Required for admin user (for regular user will be filled automatically)
  page?: number // Page number
  sort?: string // Sort by column [{column}, -{column}] Example: -id
  pageSize?: number // Page size [1,100]
  all?: 1 | 0 // Show all records with pager. Available values: 1, 0. Default value: 0
  order_item_id?: string // Order item identifier
  order_id?: string // Order identifier
  order_status?: string // Order status (multisearch). Available values: Shipped, Canceled, Pending, Unshipped, PartiallyShipped, Unfulfillable, InvoiceUnconfirmed, PendingAvailability
  seller_id?: string // Seller identifier
  seller_sku?: string // Seller SKU
  marketplace_id?: string | string[] | undefined // Marketplace identifier - diff for BE side
  item_price?: number // Item price
  quantity?: number // Quantity
  quantity_refunded?: number // Quantity refunded
  offer_type?: string // Offer type. Available values: B2B, B2C
  amazon_fees_amount?: number // Amazon fees amount
  expenses_amount?: number // Expenses amount
  total_expenses?: number // Total Expenses amount
  revenue_amount?: number // Revenue amount
  total_income?: number // Total income amount
  estimated_profit_amount?: number // Estimated profit amount
  promotion_amount?: number // Promotion amount
  product_id?: number // Product identifier
  product_asin?: string // Product ASIN
  product_title?: string // Product title
  product_ean?: string // Product EAN
  product_upc?: string // Product UPC
  product_isbn?: string // Product ISBN
  product_brand?: string // Product Brand
  product_type?: string // Product Type
  product_manufacturer?: string // Product Manufacturer
  product_parent_asin?: string // Parent Asin
  adult_product?: 1 | 0 // Product Adult. Available values: 1, 0
  product_stock_type?: string // Product stock type
  product_condition?: string // Product condition
  currency_id?: string // Currency identifier
  order_purchase_date?: string // Order purchase date
}

export type GetAmazonOrderResponse = {
  fileName?: string
  blob?: Blob
  data?: OrderType[]
}

export type GetAmazonOrderStatusesParams = {
  customerId?: number
}

export type GetAmazonOrderStatusesResponse = {
  fileName?: string
  blob?: Blob
  data?: Record<OrderStatus, string>
}

export type GetAmazonOrderLastUpdateDateResponse = {
  fileName?: string
  blob?: Blob
  data?: {
    updatedAt?: string
  }
}

export type GetAmazonOrderLastUpdateDateParams = {
  customerId?: number
}

export type AmazonOrderAmountCostResponseData = {
  currency_id?: string
  items?: OrderAmountCost[]
}

export type GetAmazonOrderAmountCostResponse = {
  fileName?: string
  blob?: Blob
  data?: AmazonOrderAmountCostResponseData
}

export type GetAmazonOrderAmountCostParams = {
  customerId?: number
  amazonOrderId: string
  salesCategoryId?: string
}

export type PostAmazonOrderUpdateAmountCostResponse = {
  fileName?: string
  blob?: Blob
  data?: {
    result?: string
  }
}
export type PostAmazonOrderUpdateAmountCostParams = {
  params: {
    customerId?: string
    amazonOrderId: string
    salesCategoryId?: string
  }
  payload: {
    currency_id?: string
    items?: OrderAmountCost[]
  }
}
