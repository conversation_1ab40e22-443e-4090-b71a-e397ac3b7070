import { getUrlSearchParamsString } from "@develop/fe-library/dist/utils"

import { profitApi } from "utils/request"

import { API_VERSION } from "constants/request"

import { GetProductAggregatedSalesInfoArgs } from "./productAggregatedSalesInfoServiceTypes"

const { APP_BAS_API_HOST_VERSION } = API_VERSION

const rootPath = `/v${APP_BAS_API_HOST_VERSION}`

const getProductAggregatedSalesInfo = ({
  params,
  customerId,
}: GetProductAggregatedSalesInfoArgs) => {
  return profitApi.get(
    `${rootPath}/product-aggregated-sales-info${getUrlSearchParamsString({
      params: { ...params, customerId },
    })}`,
  )
}

const postProductAggregatedSalesInfoExport = ({
  params,
  customerId,
}: GetProductAggregatedSalesInfoArgs) => {
  return profitApi.post(
    `${rootPath}/product-aggregated-sales-info/export${getUrlSearchParamsString(
      {
        params: { ...params, customerId },
      },
    )}`,
  )
}

export { getProductAggregatedSalesInfo, postProductAggregatedSalesInfoExport }
