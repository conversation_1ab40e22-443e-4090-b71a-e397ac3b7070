import { getUrlSearchParamsString } from "@develop/fe-library/dist/utils"

import { profitApi } from "utils/request"

import { API_VERSION } from "constants/request"

const rootPath = `/v${API_VERSION.APP_BAS_API_HOST_VERSION}/`

export const getProductExport = ({ id, customerId }) =>
  profitApi.get(
    `${rootPath}data-export/${id}${getUrlSearchParamsString({
      params: { customerId },
    })}`,
  )

export const getProductExports = (params) =>
  profitApi.get(`${rootPath}data-export${getUrlSearchParamsString({ params })}`)

export const addProductExport = ({ customerId, ...payload }) =>
  profitApi.post(
    `${rootPath}data-export/export${getUrlSearchParamsString({
      params: {
        customerId,
      },
    })}`,
    payload,
  )

export const deleteProductExport = ({ id, customerId }) =>
  profitApi.delete(
    `${rootPath}data-export/${id}${getUrlSearchParamsString({
      params: { customerId },
    })}`,
  )

export const downloadTemplateFile = (params) =>
  profitApi.get(
    `${rootPath}data-import/template${getUrlSearchParamsString({ params })}`,
    undefined,
    undefined,
    undefined,
    true,
  )

export default {
  deleteProductExport,
  downloadTemplateFile,
  getProductExports,
  getProductExport,
  addProductExport,
}
