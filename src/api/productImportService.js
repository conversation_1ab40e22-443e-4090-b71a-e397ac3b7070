import { getUrlSearchParamsString } from "@develop/fe-library/dist/utils"

import config from "config"

import { post, profitApi } from "utils/request"

import { API_VERSION } from "../constants"

const { apiVersion } = config
const rootPath = `/v${API_VERSION.APP_BAS_API_HOST_VERSION}/`

export const generatePreSignedUrl = ({ objectKey, objectType }) =>
  post(`/v${apiVersion}/amazon-s3/generate-pre-signed-url`, {
    objectKey,
    objectType,
  })

export const getProductImport = ({ id, customerId }) =>
  profitApi.get(
    `${rootPath}data-import/${id}${getUrlSearchParamsString({
      params: { customerId },
    })}`,
  )

export const getProductImports = (params) =>
  profitApi.get(`${rootPath}data-import${getUrlSearchParamsString({ params })}`)

export const deleteProductImport = ({ id, customerId }) =>
  profitApi.delete(
    `${rootPath}data-import/${id}${getUrlSearchParamsString({
      params: { customerId },
    })}`,
  )

export const downloadTemplateFile = (params) =>
  profitApi.get(
    `${rootPath}data-import/template${getUrlSearchParamsString({ params })}`,
    undefined,
    undefined,
    undefined,
    true,
  )

export const uploadProductImport = ({ data, customerId }) =>
  profitApi.post(
    `${rootPath}data-import/import${getUrlSearchParamsString({
      params: { customerId },
    })}`,
    data,
    undefined,
    undefined,
    true,
    false,
  )

export default {
  generatePreSignedUrl,
  deleteProductImport,
  downloadTemplateFile,
  getProductImport,
  getProductImports,
  uploadProductImport,
}
