import { getUrlSearchParamsString } from "@develop/fe-library/dist/utils"

import { profitApi } from "utils/request"

import { API_VERSION } from "constants/request"

const rootPath = `/v${API_VERSION.APP_BAS_API_HOST_VERSION}/`

export const getRecurrentImportSettings = ({ customerId }) =>
  profitApi.get(
    `${rootPath}data-import-recurrent${getUrlSearchParamsString({
      params: {
        customerId,
        all: 1,
        sort: "-id",
      },
    })}`,
  )

export const addRecurrentImportSetting = ({ customerId, payload }) =>
  profitApi.post(
    `${rootPath}data-import-recurrent${getUrlSearchParamsString({
      params: {
        customerId,
      },
    })}`,
    payload,
  )

export const updateRecurrentImportSetting = ({ customerId, id, payload }) =>
  profitApi.put(
    `${rootPath}data-import-recurrent/${id}${getUrlSearchParamsString({
      params: {
        customerId,
      },
    })}`,
    payload,
  )

export const deleteRecurrentImportSetting = ({ customerId, id }) =>
  profitApi.delete(
    `${rootPath}data-import-recurrent/${id}${getUrlSearchParamsString({
      params: {
        customerId,
      },
    })}`,
  )

export default {
  getRecurrentImportSettings,
  addRecurrentImportSetting,
  updateRecurrentImportSetting,
  deleteRecurrentImportSetting,
}
