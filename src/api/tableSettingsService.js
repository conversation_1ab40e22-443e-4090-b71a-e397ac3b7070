import config from "config"

import { get as getRequest, post, put } from "utils/request"

const { apiVersion } = config

export const get = (key, isDefault = 0) =>
  getRequest(
    `/v${apiVersion}/user-setting?key=${key}&all=1&default=${isDefault}`,
  )

export const save = (key, settings) =>
  post(`/v${apiVersion}/user-setting`, {
    key,
    value: settings,
  })

export const update = (id, settings) =>
  put(`/v${apiVersion}/user-setting?id=${id}`, {
    value: settings,
  })

export const saveDefault = (key, settings) =>
  post(`/v${apiVersion}/user-setting?key=${key}`, {
    user_id: null,
    key: key,
    value: settings,
    default: 1,
  })

export const updateDefault = (id, key, settings) =>
  put(`/v${apiVersion}/user-setting?id=${id}`, {
    user_id: null,
    key: key,
    value: settings,
    default: 1,
  })

export default {
  get,
  save,
  update,
  saveDefault,
  updateDefault,
}
