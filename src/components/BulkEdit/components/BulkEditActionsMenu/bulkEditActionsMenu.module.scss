@import "assets/styles/variables";


.container {
  position: absolute;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  padding-left: 10px;
  left: 0;
  right: 0;
  background: var(--color-background-second);
  height: 52px;
  top: -52px;
  z-index: 10;
  & .selected {
    min-width: 90px;
    text-align: left;
  }
   & .button {
    display: flex;
    align-items: center;
    color: var(--color-text-main);
    font-size: 13px;
    margin-left: 40px;
    position: relative;
    cursor: pointer;
    gap: var(--gap-m);

    &:hover svg,
    &:hover p {
      color: var(--color-text-link);
    }
   }
   @include mediaMax($lg - 1) {
    & .selected {
      min-width: 50px;
      text-align: left;
    }
    & .button {
      margin-left: 24px;
    }
   }
   @include mediaMax($md - 1) {
    & .button {
      margin-left: 18px;
      & .title {
        display: none;
      }
    }
   }
}

.modalContainer {
  padding-right: 15px;
}
.buttonTitle {
  display: inline-block;
}
