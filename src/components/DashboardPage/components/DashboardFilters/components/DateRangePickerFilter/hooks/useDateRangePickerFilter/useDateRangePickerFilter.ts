import { useCallback, useEffect, useState } from "react"
import { useSelector } from "react-redux"
import { useHistory } from "react-router-dom"
import { InputMode } from "@develop/fe-library"
import { getUrlSearchParamsString } from "@develop/fe-library/dist/utils"
import { format, Interval, subDays } from "date-fns"

import {
  languageSelector,
  translationsSelector,
} from "selectors/mainStateSelectors"

import { useProductsAndFilters } from "components/SegmentsSidebar/hooks"

import { useMinStatsDate, useUrlParams } from "hooks"
import { useDateRangeLabels } from "hooks/useDateRangeLabels"
import { useKPIWidgets } from "hooks/useKPIWidgets"

import { getDateRangeInterval, validateInputMode } from "utils/dateRange"

import { CLEAR_TO_CLOSE_LABEL } from "constants/dateRange"
import { DATE_FNS_FORMATS } from "constants/dateTime"

import { URL_PARAMS_TO_WATCH } from "./constants"

import { DashboardFiltersParams } from "components/SegmentsSidebar/components/Filters/FiltersTypes"

import {
  DateRangePickerValue,
  GetSelectedRangeParams,
} from "./useDateRangePickerFilterTypes"

export const useDateRangePickerFilter = () => {
  const { locale } = useSelector(translationsSelector)
  const language = useSelector(languageSelector)
  const history = useHistory()

  const { isLoadingProductsOrFilters } = useProductsAndFilters()
  const { urlParams, prevUrlParams } = useUrlParams<DashboardFiltersParams>()
  const labels = useDateRangeLabels(CLEAR_TO_CLOSE_LABEL)
  const { minStatsDate, today, minStatsDateLoading, minStatsDateSuccess } =
    useMinStatsDate()
  const { setAndSelectFirstWidget } = useKPIWidgets()

  const getFallbackInterval = (): Interval => ({
    start: subDays(new Date(), 13),
    end: new Date(),
  })

  const getSelectedRange = useCallback(
    ({ from, inputMode, to }: GetSelectedRangeParams): Interval => {
      // FIXME: This is a temporary fix, need to cover whole pages to wait for minStatsDate
      if (!from || !to) {
        return getFallbackInterval()
      }

      try {
        const interval = getDateRangeInterval({
          from: from ? new Date(from) : undefined,
          to: to ? new Date(to) : undefined,
          inputMode: validateInputMode(inputMode),
          fromDate: minStatsDate,
          toDate: today,
        })

        if (!interval) {
          return getFallbackInterval()
        }

        return interval
      } catch (error) {
        return getFallbackInterval()
      }
    },
    [minStatsDate, today],
  )

  const [inputMode, setInputMode] = useState<InputMode>(
    validateInputMode(urlParams.inputMode),
  )
  const [selected, setSelected] = useState<Interval>(() =>
    getSelectedRange({
      from: urlParams.from,
      to: urlParams.to,
      inputMode: urlParams.inputMode,
    }),
  )

  const handleDateRangePickerSelect = (value: DateRangePickerValue) => {
    if (!value.selected || !value.inputMode) {
      return
    }

    setSelected(value.selected)
    setInputMode(value.inputMode)

    setAndSelectFirstWidget({
      inputMode: value.inputMode,
      selected: value.selected,
    })
  }

  useEffect(() => {
    const hasDateRangeChanged: boolean =
      format(selected.start, DATE_FNS_FORMATS.SERVER) !== urlParams.from ||
      format(selected.end, DATE_FNS_FORMATS.SERVER) !== urlParams.to ||
      inputMode !== urlParams.inputMode

    if (!hasDateRangeChanged) {
      return
    }

    history.push({
      ...history.location,
      search: getUrlSearchParamsString({
        params: {
          ...urlParams,
          from: format(selected.start, DATE_FNS_FORMATS.SERVER),
          to: format(selected.end, DATE_FNS_FORMATS.SERVER),
          inputMode,
        },
      }),
    })
  }, [selected, inputMode])

  useEffect(() => {
    const hasDateRangeChanged: boolean = URL_PARAMS_TO_WATCH.some(
      (key) => prevUrlParams?.[key] !== urlParams[key],
    )

    if (hasDateRangeChanged) {
      try {
        const newSelected = getSelectedRange({
          from: urlParams.from,
          to: urlParams.to,
          inputMode: urlParams.inputMode,
        })

        setSelected(newSelected)
        setInputMode(validateInputMode(urlParams.inputMode))
      } catch (error) {
        console.error("Error updating date range:", error)
        setSelected(getFallbackInterval())
        setInputMode(validateInputMode(urlParams.inputMode))
      }
    }
  }, [urlParams.from, urlParams.to, urlParams.inputMode])

  return {
    locale,
    language,
    isDisabled:
      isLoadingProductsOrFilters ||
      (minStatsDateLoading && !minStatsDateSuccess),
    labels,
    inputMode,
    fromDate: minStatsDate,
    toDate: today,
    selected,
    handleDateRangePickerSelect,
  }
}
