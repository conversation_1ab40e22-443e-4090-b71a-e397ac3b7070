import React, { useCallback } from "react"
import { useSelector } from "react-redux"
import { useHistory } from "react-router-dom"
import { getUrlSearchParamsString } from "@develop/fe-library/dist/utils"

import { groupAccountOptionsSelector } from "selectors/groupAccountSelectors"

import {
  useDashboardFilters,
  useToggleFilters,
} from "components/DashboardPage/components/DashboardFilters/hooks"
import { AddNewGroup } from "components/SegmentsSidebar/components/Filters/components/MainFilter/components"
import { useProductsAndFilters } from "components/SegmentsSidebar/hooks"

import { useMarketplaceOptions, useSubscription, useUrlParams } from "hooks"

import {
  GLOBAL_GROUP_ACCOUNT_OPTION,
  GROUP_ACCOUNT_TYPE,
} from "constants/groupAccount"

import { DashboardFiltersParams } from "components/SegmentsSidebar/components/Filters/FiltersTypes"

import { ProductsSalesInfoTableUrlParams } from "types/Tables/ProductsSalesInfoTable/ProductsSalesInfoTable"

import {
  TableMarketplaceIdUrlParams,
  TableSellerIdUrlParams,
} from "./useGroupAccountFilterTypes"
import {
  IsTogglePreventedParams,
  Value,
} from "@develop/fe-library/dist/lib/components/Select/SelectTypes"

export const useGroupAccountFilter = () => {
  const history = useHistory()
  const { options: groupAccountOptions } = useSelector(
    groupAccountOptionsSelector,
  )

  const { isLoadingProductsOrFilters } = useProductsAndFilters()
  const { urlParams } = useUrlParams<
    DashboardFiltersParams & ProductsSalesInfoTableUrlParams
  >()
  const { isBasSubscriptionActive } = useSubscription()
  const { isProductFiltersApplied } = useDashboardFilters()
  const { handleToggleSidebar } = useToggleFilters()
  const { getMarketplaceOptions } = useMarketplaceOptions()

  const selectedGroupAccount = groupAccountOptions.find(
    (option) => option.value === urlParams.seller_id,
  )

  const handleGroupAccountChange = useCallback(
    (value: Value) => {
      const seller_id: string | undefined =
        value !== GROUP_ACCOUNT_TYPE.GLOBAL ? (value as string) : undefined

      const getNextMarketplaces = (): string => {
        const internalSellerId: string = seller_id || GROUP_ACCOUNT_TYPE.GLOBAL
        const nextMarketplaceOptions = getMarketplaceOptions(internalSellerId)

        if (!urlParams.marketplace_id) {
          return ""
        }

        const filteredSelectedMarketplaceOptions =
          nextMarketplaceOptions.filter(({ value }) => {
            return urlParams.marketplace_id?.includes(value)
          })

        return filteredSelectedMarketplaceOptions
          .map(({ value }) => value)
          .join(",")
      }

      const nextMarketplaces: string = getNextMarketplaces()

      const products_seller_id: TableSellerIdUrlParams =
        !seller_id || urlParams["products-seller_id"] === seller_id
          ? {}
          : {
              "products-seller_id": undefined,
            }

      const products_marketplace_id: TableMarketplaceIdUrlParams =
        nextMarketplaces.includes(urlParams["products-marketplace_id"]!)
          ? {}
          : {
              "products-marketplace_id": undefined,
            }

      history.push({
        ...history.location,
        search: getUrlSearchParamsString({
          params: {
            ...urlParams,
            ...products_seller_id,
            ...products_marketplace_id,
            seller_id,
            marketplace_id: getNextMarketplaces(),
          },
        }),
      })
    },
    [getMarketplaceOptions, history, urlParams],
  )

  const handleTogglePrevented = useCallback(
    ({ open, event }: IsTogglePreventedParams): boolean => {
      if (isProductFiltersApplied) {
        event.stopPropagation()

        handleToggleSidebar()

        return true
      }

      return false
    },
    [handleToggleSidebar, isProductFiltersApplied],
  )

  const renderDropdownMenu = useCallback(
    ({ menu }): JSX.Element => (
      <>
        {isBasSubscriptionActive ? <AddNewGroup /> : null}
        {menu}
      </>
    ),
    [isBasSubscriptionActive],
  )

  return {
    selectedGroupAccount,
    groupAccountOptions,
    isDisabled: isLoadingProductsOrFilters,
    renderDropdownMenu,
    value: !urlParams.seller_id
      ? GLOBAL_GROUP_ACCOUNT_OPTION.value
      : urlParams.seller_id,
    handleGroupAccountChange,
    handleTogglePrevented,
  }
}
