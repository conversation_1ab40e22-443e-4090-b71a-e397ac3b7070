import React, { useCallback, useEffect, useState } from "react"
import { useDispatch, useSelector } from "react-redux"
import { useHistory } from "react-router"
import { Option } from "@develop/fe-library"
import { getUrlSearchParamsString } from "@develop/fe-library/dist/utils"
import debounce from "lodash.debounce"

import { salesHistoryActions } from "actions/salesHistoryActions"

import {
  productsTopFilterSelector,
  productsTopFilterStatusSelector,
} from "selectors/salesHistorySelectors"

import { useDashboardFilters } from "components/DashboardPage/components/DashboardFilters/hooks"
import { DASHBOARD_PRODUCT_RESET } from "components/DashboardPage/constants"
import { ProductFilterSelectOption } from "components/SegmentsSidebar/components/Filters/components/ProductFilters/components/ProductFilterSelectOption"
import { useProductFilterDropdownMenu } from "components/SegmentsSidebar/components/Filters/components/ProductFilters/hooks"
import { useProductsAndFilters } from "components/SegmentsSidebar/hooks"

import { useMarketplaceSellerIds, useUrlParams } from "hooks"
import usePrevious from "hooks/usePrevious"

import { checkIsArray } from "utils/arrayHelpers"
import { FormattedProduct } from "utils/getFormattedProducts"
import l from "utils/intl"

import { ASYNC_STATUSES } from "constants/async"

import { URL_PARAMS_KEYS_TO_COMPARE } from "./constants"

import { DashboardFiltersParams } from "components/SegmentsSidebar/components/Filters/FiltersTypes"

import { AsyncStatus, ProductRequestParams } from "types"
import { ProductOptionType } from "types/store/salesHistoryReducer"

import { FetchSelectedProductTopFilterParams } from "./useProductFilterTypes"

const { getProductsTopFilter, clearProductsTopFilter } = salesHistoryActions

export const useProductFilter = () => {
  const dispatch = useDispatch()
  const history = useHistory()

  const [selectedValue, setSelectedValue] = useState<number | null>(null)
  const [searchValue, setSearchValue] = useState("")
  const [isDropdownOpen, setIsDropdownOpen] = useState(false)

  const productsTopFilter: FormattedProduct[] = useSelector(
    productsTopFilterSelector,
  )
  const productsTopFilterStatus: AsyncStatus = useSelector(
    productsTopFilterStatusSelector,
  )
  const isProductsTopFilterLoading: boolean =
    productsTopFilterStatus === ASYNC_STATUSES.PENDING
  const isProductsTopFilterIdle: boolean =
    productsTopFilterStatus === ASYNC_STATUSES.IDLE

  const { urlParams } = useUrlParams<DashboardFiltersParams>()
  const prevUrlParams = usePrevious(urlParams)
  const { isLoadingProductsOrFilters, isProductsFulfilledOrRejected } =
    useProductsAndFilters()
  const { isProductFiltersApplied, isProductApplied } = useDashboardFilters()
  const { renderProductDropdownMenu } = useProductFilterDropdownMenu()
  const { getMarketplaceSellerIds } = useMarketplaceSellerIds()

  const [options, setOptions] = useState<ProductOptionType[]>([])

  const fetchProductsTopFilter = useCallback(
    (
      params: ProductRequestParams,
      onSuccess = () => {},
      onFailure = () => {},
    ): void => {
      dispatch(getProductsTopFilter(params, onSuccess, onFailure))
    },
    [dispatch],
  )

  const fetchSelectedProductTopFilter = useCallback(
    ({ isInitialRun = false }: FetchSelectedProductTopFilterParams = {}) => {
      const { marketplace_id, seller_id, marketplaceSellerIds } =
        getMarketplaceSellerIds(urlParams)

      const requestParams: ProductRequestParams = isInitialRun
        ? {
            sku: `=${urlParams.productSku}`,
            seller_id: urlParams.productSeller,
            marketplace_id: urlParams.productMarketplace,
          }
        : {
            sku: `=${urlParams.productSku}`,
            seller_id: seller_id,
            marketplace_id: marketplace_id,
            marketplaceSellerIds,
          }

      fetchProductsTopFilter(requestParams)
    },
    [fetchProductsTopFilter, getMarketplaceSellerIds, urlParams],
  )

  const handleSearch = useCallback(
    debounce((value: string) => {
      const { marketplace_id, marketplaceSellerIds, seller_id } =
        getMarketplaceSellerIds(urlParams)

      const requestParams: ProductRequestParams = {
        marketplace_id,
        marketplaceSellerIds,
        seller_id,
        search: value,
        adult_product: urlParams.adult_product === "true" ? "1" : undefined,
        asin: urlParams.asin,
        sku: urlParams.sku,
        brand: urlParams.brand,
        ean: urlParams.ean,
        manufacturer: urlParams.manufacturer,
        parent_asin: urlParams.parent_asin,
        product_type: urlParams.product_type,
        stock_type: urlParams.stock_type,
        upc: urlParams.upc,
      }

      fetchProductsTopFilter(requestParams)
    }, 500),
    [urlParams],
  )

  const handleProductSelect = useCallback(
    (option: Option): void => {
      if (!option) {
        return
      }

      const product = option as ProductOptionType

      const getNextMarketplaces = (): DashboardFiltersParams => {
        const marketplaces = urlParams.marketplace_id?.split(",") || []

        const shouldResetMarketplaces: boolean =
          checkIsArray(marketplaces) &&
          !marketplaces.some(
            (marketplaceId) => marketplaceId === product.marketplace_id,
          )

        return shouldResetMarketplaces ? { marketplace_id: undefined } : {}
      }

      setSearchValue("")
      setSelectedValue(product.id!)
      setOptions([product])

      history.push({
        ...history.location,
        search: getUrlSearchParamsString({
          params: {
            ...urlParams,
            ...getNextMarketplaces(),
            productId: String(product.id),
            productASIN: product.asin,
            productMarketplace: product.marketplace_id,
            productSeller: product.seller_id,
            productSku: product.sku,
          },
        }),
      })
    },
    [history, urlParams],
  )

  const handleSearchValueChange = (value: string): void => {
    setSearchValue(value)
  }

  const handleClear = useCallback((): void => {
    history.push({
      ...history.location,
      search: getUrlSearchParamsString({
        params: {
          ...urlParams,
          ...DASHBOARD_PRODUCT_RESET,
        },
      }),
    })
  }, [history, urlParams])

  const clearProductsSelection = useCallback(() => {
    setOptions([])
    setSearchValue("")
    setSelectedValue(null)
    dispatch(clearProductsTopFilter())
  }, [dispatch])

  const handleDropdownClose = useCallback(
    (isVisible: boolean) => {
      if (isVisible) {
        return
      }

      if (!isProductApplied) {
        clearProductsSelection()

        return
      }

      const isOptionsContainAppliedProduct: boolean = options.some(
        (option) =>
          option.marketplace_id === urlParams.productMarketplace &&
          option.seller_id === urlParams.productSeller &&
          option.sku === urlParams.productSku,
      )

      if (!isOptionsContainAppliedProduct && !isProductsTopFilterLoading) {
        fetchSelectedProductTopFilter({
          isInitialRun: true,
        })
      }
    },
    [
      clearProductsSelection,
      fetchSelectedProductTopFilter,
      isProductApplied,
      isProductsTopFilterLoading,
      options,
      urlParams.productMarketplace,
      urlParams.productSeller,
      urlParams.productSku,
    ],
  )

  const handleFilterOptions = (): boolean => {
    return true
  }

  const handleIsTogglePrevented = useCallback(
    ({ open }: { open: boolean }): boolean => {
      setIsDropdownOpen(open)

      return open
    },
    [],
  )

  // DESC: Fetch products by searchValue
  useEffect(() => {
    const searchValueTrimmed: string = searchValue.trim()

    if (searchValueTrimmed) {
      handleSearch(searchValueTrimmed)
    }
  }, [searchValue])

  // DESC: Set new Options on productsTopFilter
  useEffect(() => {
    if (productsTopFilterStatus !== ASYNC_STATUSES.FULFILLED) {
      return
    }

    const products: FormattedProduct[] = checkIsArray(productsTopFilter)
      ? productsTopFilter
      : []

    if (!checkIsArray(products) && !isDropdownOpen) {
      history.push({
        ...history.location,
        search: getUrlSearchParamsString({
          params: {
            ...urlParams,
            ...DASHBOARD_PRODUCT_RESET,
          },
        }),
      })

      clearProductsSelection()

      return
    }

    let selectedProduct: FormattedProduct | undefined = undefined

    const hasProductChanged: boolean =
      String(selectedValue) !== urlParams.productId

    if (hasProductChanged) {
      selectedProduct = products.find(
        (product) => String(product.id) === urlParams.productId,
      )
    }

    const options: FormattedProduct[] = selectedProduct
      ? [selectedProduct]
      : products

    if (selectedProduct) {
      setSelectedValue(selectedProduct.id)
    }

    setOptions(
      options.map((product) => ({
        ...product,
        label: product?.title,
        value: product?.id,
      })) as unknown as ProductOptionType[],
    )
  }, [productsTopFilter, urlParams])

  // DESC: handling urlParams changes
  useEffect(() => {
    if (!isProductApplied) {
      clearProductsSelection()

      return
    }

    if (!prevUrlParams && !isProductsTopFilterLoading) {
      fetchSelectedProductTopFilter({
        isInitialRun: true,
      })

      return
    }

    const hasUrlParamsChanged: boolean = URL_PARAMS_KEYS_TO_COMPARE.some(
      (key) => urlParams[key] !== prevUrlParams?.[key],
    )

    if (hasUrlParamsChanged && !isProductsTopFilterLoading) {
      fetchSelectedProductTopFilter()
    }
  }, [urlParams])

  const renderOption = useCallback(
    ({ option }: { option: ProductOptionType }) => (
      <ProductFilterSelectOption
        isAsin
        isSku
        isMarketplaceHidden={false}
        maxWidth="100%"
        option={option}
        searchValue={searchValue}
      />
    ),
    [searchValue],
  )

  const isOptionsEmpty: boolean = !options.length

  const isIdle: boolean =
    isProductsTopFilterIdle && (!setSelectedValue || isOptionsEmpty)

  const isDisabled: boolean =
    isLoadingProductsOrFilters ||
    isProductFiltersApplied ||
    isProductsTopFilterLoading

  const label: string = isProductFiltersApplied
    ? l("Advanced filter is applied")
    : l("Search by (SKU, ASIN, Product name)")

  return {
    selectedValue,
    options,
    isDisabled,
    renderOption,
    searchValue,
    label,
    handleSearchValueChange,
    renderProductDropdownMenu,
    handleIsTogglePrevented,
    handleProductSelect,
    handleClear,
    handleFilterOptions,
    handleDropdownClose,
    isIdle,
    isLoading: isLoadingProductsOrFilters || isProductsTopFilterLoading,
    isEmpty: isProductsFulfilledOrRejected && isOptionsEmpty,
  }
}
