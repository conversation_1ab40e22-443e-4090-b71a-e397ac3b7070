import React from "react"
import { Box } from "@develop/fe-library"

import { SalesHistoryWidget } from "components/SalesHistoryWidget"
import { ProductToLookAt } from "components/Widgets"

import { DashboardFilters } from "../DashboardFilters"
import { DashboardTableWidgets } from "../DashboardTableWidgets"

export const DashboardStatisticsPage = () => {
  return (
    <Box
      flexDirection="column"
      gap="m"
      overflowX="hidden"
      padding="m m 0"
      mXL={{
        padding: "l l 0",
        gap: "l",
      }}
      tb={{
        maxWidth: "calc(100vw - 75px)",
      }}
    >
      <DashboardFilters />

      <SalesHistoryWidget />

      <DashboardTableWidgets />

      <ProductToLookAt />
    </Box>
  )
}
