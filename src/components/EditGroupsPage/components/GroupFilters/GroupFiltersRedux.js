import { connect } from 'react-redux'
import GroupFiltersView from './GroupFiltersView'
import marketplaceGroupsActions from 'actions/marketplaceGroupsActions'

const { getAllMarketplaceGroups, deleteGroup } = marketplaceGroupsActions

const mapStateToProps = (state) => {
  const { marketplaceGroups } = state

  return {
    searchOptions: marketplaceGroups.searchOptions,
  }
}

const mapDispatchToProps = (dispatch) => ({
  getMarketplaceGroups: (searchOptions) => {
    return dispatch(getAllMarketplaceGroups(searchOptions))
  },

  deleteMarketplaceGroup: (payload, successCallback, failureCallback) => {
    return dispatch(deleteGroup(payload, successCallback, failureCallback))
  },
})

export default connect(mapStateToProps, mapDispatchToProps)(GroupFiltersView)
