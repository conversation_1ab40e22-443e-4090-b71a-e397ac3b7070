import React from "react"
import PropTypes from "prop-types"
import { ROUTES } from "@develop/fe-library/dist/routes"

import ErrorIcon from "./components/ErrorIcon"
import Typography from "components/Typography"
import FormattedMessage from "components/FormattedMessage"

import config from "config"

import styles from "components/ErrorNotification/errorNotification.module.scss"

const { showErrorDetails } = config

const ErrorNotificationView = ({
  description,
  errorCode,
  message,
  showHomeLink,
  title,
}) => (
  <div className={styles.wrapper}>
    <div className={styles.container}>
      <ErrorIcon />
      <Typography className={styles.errorLabel}>
        <FormattedMessage id={title} />
      </Typography>
      <Typography className={styles.wrongLabel}>
        <FormattedMessage id={description} />
      </Typography>
      {message && showErrorDetails && (
        <div className={styles.errorCodeContainer}>
          <Typography
            className={styles.errorCodeLabel}
            type="span"
            variant="textLarge"
          >
            <FormattedMessage id="Message" />
            {": "}
          </Typography>
          <Typography
            className={styles.errorCode}
            type="span"
            variant="textLarge"
          >
            {message}
          </Typography>
        </div>
      )}
      {errorCode && showErrorDetails && (
        <div className={styles.errorCodeContainer}>
          <Typography
            className={styles.errorCodeLabel}
            type="span"
            variant="textLarge"
          >
            <FormattedMessage id="Code error" />
            {": "}
          </Typography>
          <Typography
            className={styles.errorCode}
            type="span"
            variant="textLarge"
          >
            {errorCode}
          </Typography>
        </div>
      )}
      {showHomeLink && (
        <a className={styles.button} href={ROUTES.GENERAL_ROUTES.PATH_HOME}>
          <Typography className={styles.buttonLabel} variant="controlLabel">
            <FormattedMessage id="Go to main page" />
          </Typography>
        </a>
      )}
    </div>
  </div>
)

ErrorNotificationView.propTypes = {
  description: PropTypes.string,
  errorCode: PropTypes.string,
  message: PropTypes.string,
  showHomeLink: PropTypes.bool,
  title: PropTypes.string,
}

ErrorNotificationView.defaultProps = {
  description: "Something went wrong",
  showHomeLink: true,
  title: "Error!",
}

export default ErrorNotificationView
