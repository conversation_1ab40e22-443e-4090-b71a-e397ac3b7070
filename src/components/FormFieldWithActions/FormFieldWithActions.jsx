import React, { useCallback, useEffect, useRef, useState } from "react"
import FocusLock from "react-focus-lock"
import { Box, Button, Typography } from "@develop/fe-library"
import { getObjectKeys } from "@develop/fe-library/dist/utils"
import cn from "classnames"
import { Field, Form, Formik } from "formik"
import PropTypes from "prop-types"

import FormattedMessage from "components/FormattedMessage"
import { FormKeypressHandler } from "components/KeypressHandler"
import getMainApp from "components/MainAppProvider"
import { TextField, withErrorField } from "components/shared/FormFields"
import { RestrictedPrimaryButton } from "components/shared/RestrictedButtons"

import l from "utils/intl"

import { GLOBAL_NOTIFICATION_TYPES } from "constants/globalNotifications"

import styles from "./FormFieldWithActions.module.scss"

const TextFieldWithError = withErrorField(TextField, true, false)

const DISPLAY_TYPES = {
  inline: "inline",
  block: "block",
}

export const FormFieldWithActions = ({
  fieldName,
  onClose,
  onUpdate: propsOnUpdate,
  renderField,
  title,
  actionsDisplayType,
  formClassName,
  primaryButtonProps,
  /* If you need additional ref for you case, like when you use specific onClick, onChange or etc
    for ref you could use additionalCustomRefPrimaryButton. This is for private cases, not general.
  */
  additionalCustomRefPrimaryButton,
  hasFormKeypressHandler,
  fieldWrapperClassName,
  customButtonsContainerClassName,
  ...formikProps
}) => {
  const [innerFocus, setInnerFocus] = useState(false)

  const primaryButtonRef = useRef(null)

  const submitButtonRef =
    additionalCustomRefPrimaryButton || primaryButtonRef?.current

  const onError = useCallback((errors, values) => {
    const errorKeys = getObjectKeys(errors)
    const valueKeys = getObjectKeys(values)

    if (valueKeys.includes(errorKeys[0])) {
      return
    }

    const mainApp = getMainApp()

    mainApp?.actions?.showGlobalNotification?.({
      type: GLOBAL_NOTIFICATION_TYPES.ERROR,
      message: l(
        "An error has occurred while validating product fields. Please edit the product via the “update“, to check for any validation error.",
      ),
      isCustomMessage: true,
      duration: 8000,
    })
  }, [])

  const onUpdate = (values, setErrors) => {
    propsOnUpdate(values, (errors) => {
      if (fieldName.includes("productSetting")) {
        const [, name] = fieldName.split(".")

        errors[fieldName] = errors[name]
      } else {
        onError(errors, values)
      }
      setErrors(errors)
    })
  }

  const handleFormSubmit = ({ values, setErrors }) => {
    return () => {
      return onUpdate(values, setErrors)
    }
  }

  const handleKeypressSubmit = ({ values, setErrors }) => {
    return () => {
      return !formikProps.isSubmitting && onUpdate(values, setErrors)
    }
  }

  useEffect(() => {
    setTimeout(() => setInnerFocus(true), 500)
  }, [])

  useEffect(() => {
    const isNeedFocusToPrimaryButton =
      primaryButtonRef && !additionalCustomRefPrimaryButton

    if (isNeedFocusToPrimaryButton) {
      primaryButtonRef?.current?.focus()
    }
  }, [handleKeypressSubmit, handleFormSubmit, additionalCustomRefPrimaryButton])

  return (
    <FocusLock className={styles.formFocusLock} disabled={!innerFocus}>
      <Formik {...formikProps}>
        {({ values, setErrors }) => {
          const renderActionsBlock = () => (
            <Box
              gap="m"
              justify="flex-end"
              className={cn(
                styles.buttonsContainer,
                customButtonsContainerClassName,
              )}
            >
              <Button
                iconOnly
                icon="icnClose"
                name="Close"
                type="button"
                variant="secondary"
                onClick={onClose}
              />
              <RestrictedPrimaryButton
                ref={submitButtonRef}
                iconOnly
                icon="icnCheck"
                type="button"
                onClick={handleFormSubmit({ values, setErrors })}
                {...primaryButtonProps}
              />
            </Box>
          )

          return (
            <Form noValidate className={cn(styles.form, formClassName)}>
              {!hasFormKeypressHandler ? null : (
                <FormKeypressHandler
                  onClose={onClose}
                  onSubmit={handleKeypressSubmit({ values, setErrors })}
                />
              )}

              {!title ? null : (
                <Typography className="title" variant="--font-body-text-6">
                  {typeof title === "string" ? (
                    <FormattedMessage id={title} />
                  ) : (
                    title
                  )}
                </Typography>
              )}

              <Box
                align="center"
                className={cn(styles.field, fieldWrapperClassName)}
                flexDirection="column"
                gap="m"
                justify="center"
              >
                {renderField ? null : (
                  <Field
                    allowClear
                    component={TextFieldWithError}
                    name={fieldName}
                  />
                )}

                {!renderField ? null : renderField()}

                {actionsDisplayType !== DISPLAY_TYPES.inline
                  ? null
                  : renderActionsBlock()}
              </Box>

              {actionsDisplayType !== DISPLAY_TYPES.block
                ? null
                : renderActionsBlock()}
            </Form>
          )
        }}
      </Formik>
    </FocusLock>
  )
}

FormFieldWithActions.propTypes = {
  initialValues: PropTypes.object,
  title: PropTypes.oneOfType([PropTypes.string, PropTypes.node]),
  fieldName: PropTypes.string.isRequired,
  onClose: PropTypes.func.isRequired,
  onUpdate: PropTypes.func,
  renderField: PropTypes.func,
  actionsDisplayType: PropTypes.string,
  primaryButtonProps: PropTypes.object,
  hasFormKeypressHandler: PropTypes.bool,
  fieldWrapperClassName: PropTypes.string,
  customButtonsContainerClassName: PropTypes.string,
  additionalCustomRefPrimaryButton: PropTypes.oneOfType([
    PropTypes.oneOf([null]),
    PropTypes.element,
  ]),
}

FormFieldWithActions.defaultProps = {
  onClose: () => {},
  onUpdate: () => {},
  actionsDisplayType: "inline",
  formClassName: "",
  primaryButtonProps: {},
  hasFormKeypressHandler: true,
  fieldWrapperClassName: "",
  customButtonsContainerClassName: "",
  additionalCustomRefPrimaryButton: null,
}
