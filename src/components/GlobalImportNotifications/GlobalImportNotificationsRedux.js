import { connect } from "react-redux"

import productImportActions from "actions/productImportActions"

import {
  customerSelector,
  mainStateSelector,
  permissionsSelector,
  routerSelector,
} from "selectors/mainStateSelectors"
import { subscriptionsSelector } from "selectors/subscriptionsSelectors"

import { GlobalImportNotificationsContainer } from "./GlobalImportNotifications"

const { getAllImports } = productImportActions

const mapStateToProps = (state) => {
  const {
    productImport: { allProductImports, processingId },
  } = state
  const { location } = routerSelector(state)
  const { customer } = customerSelector(state)
  const { basProductCostImportList } = permissionsSelector()
  const {
    isBasSubscriptionActive,
    isRepricerSubscriptionActive,
    isFreemiumBasModelActive,
  } = subscriptionsSelector()

  return {
    path: location?.pathname,
    allProductImports,
    processingId,
    customerId: customer?.id,
    canUseImport: !!basProductCostImportList && !!customer?.id,
    history: mainStateSelector,
    isBasSubscriptionActive,
    isRepricerSubscriptionActive,
    isFreemiumBasModelActive,
  }
}

const mapDispatchToProps = (dispatch) => ({
  getAllImports: () => dispatch(getAllImports({})),
})

export const GlobalImportNotifications = connect(
  mapStateToProps,
  mapDispatchToProps,
)(GlobalImportNotificationsContainer)
