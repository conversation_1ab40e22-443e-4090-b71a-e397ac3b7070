@import "assets/styles/variables.scss";

.title.title {
  color: $text_main;
  font-size: 16px;
  font-weight: 700;
  line-height: 16px;
}

.group {
  align-items: flex-end;
}

.field {
  min-width: 220px;
  width: 100%;
  color: $text_second;
  font-size: 12px !important;
  margin-top: 1px;
  :global(.ant-select-selection-selected-value) {
    font-size: 12px !important;
  }
}

.error {
  :global(.with-error-field-icon) {
    right: 60px;
  }
}

.selectError {
  :global(.with-error-field-icon) {
    right: 80px;
  }
}

@media (max-width: $sm) {
  .buttonsContainer {
    margin-top: 10px;
    padding: 10px;
    padding-bottom: 0;
    transform: translateX(-10px);
    width: calc(100% + 20px);
  }
}

.bulkFieldTitle {
  margin: 10px 0;
  font-size: 13px;
  color: $text_main;
}

.confirmSelectedFieldsTitle {
  font-weight: 700;
  font-size: 14px;
  margin: 20px 0;
}
