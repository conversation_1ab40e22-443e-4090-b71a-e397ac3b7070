import React from "react"
import PropTypes from "prop-types"
import { useSelector } from "react-redux"

import GridProduct from "models/GridProduct"

import { allAmazonMarketplacesSelector } from "selectors/mainStateSelectors"

import { ExportValue } from "components/TableGridLayout/components/ExportValue"
import Link from "components/shared/Link"

import { getAmazonProductLink } from "utils/links"

const AsinColumn = ({ productObject, marketPlaceId = null }) => {
  const { asin, marketplace_id, product_asin } = productObject
  const marketPlaces = useSelector(allAmazonMarketplacesSelector) || []
  const currentMarketplaceId = marketPlaceId || marketplace_id

  const marketPlace = marketPlaces.find(({ id }) => id === currentMarketplaceId)

  if (!marketPlace) {
    return null
  }

  const { sales_channel } = marketPlace
  const asinValue = asin || product_asin

  return (
    <ExportValue>
      <Link
        internal={false}
        url={getAmazonProductLink(sales_channel, asinValue)}
        styleType="primary"
        type="span"
        variant="textSmall"
        text={asinValue?.toLocaleUpperCase()}
        target="_blank"
        rel="noopener noreferrer"
        style={{ display: "inline-block" }}
      />
    </ExportValue>
  )
}

AsinColumn.propTypes = {
  productObject: PropTypes.instanceOf(GridProduct),
}

export default AsinColumn
