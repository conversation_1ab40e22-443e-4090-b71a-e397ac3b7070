.dropdownWrapper.dropdownWrapper {
  box-shadow: var(--box-shadow);

  :global(.ant-picker) {
    border: var(--border-main);
    width: 100%;
  }

  :global(.ant-picker-cell:not(.ant-picker-cell-in-view)) {
    cursor: default;
    pointer-events: none;
  }

  :global(.ant-picker-focused) {
    border-color: var(--color-border-active);
  }

  :global(.ant-picker-input) {
    display: inline-flex;
  }

  :global(.ant-picker-dropdown) {
    padding-top: var(--padding-m);
    background: var(--color-main-background);
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
  }
}
