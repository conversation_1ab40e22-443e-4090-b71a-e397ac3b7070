import { useCallback, useEffect, useState } from "react"
import { useForm, useWatch } from "react-hook-form"
import { useDispatch } from "react-redux"
import { getObjectKeys } from "@develop/fe-library/dist/utils"
import moment from "moment"

// @ts-ignore
import indirectCostsActions from "actions/indirectCostsActions"

import { checkIsArray } from "utils/arrayHelpers"
import { setConfirm } from "utils/confirm"
import l from "utils/intl"

import { SERVER_DATE_FORMAT } from "constants/dateTime"
import {
  DEFAULT_CRON_EXPR,
  FREQUENCY_TYPES,
  GLOBAL_SELLER,
  ITERATION_TYPES,
  PRODUCT_FIELDS,
} from "constants/indirectCosts"

import {
  ErrorTypes,
  IndirectCostKeysTypes,
  IndirectCostType,
  ProductValuesTypes,
  UseIndirectCostModalTypes,
} from "types/IndirectCost"

const {
  createIndirectCostType,
  deleteIndirectCostType,
  setCostModalVisibility,
  updateIndirectCost,
  createIndirectCost,
  getIndirectCostTypes,
  // @ts-ignore
} = indirectCostsActions

export const useIndirectCostModal = ({
  defaultValues = {},
  currentEditCost = null,
  accountMarketplaces = {},
}: UseIndirectCostModalTypes) => {
  const dispatch = useDispatch()

  const [selectedFieldName, setSelectedFieldName] = useState<string | null>(
    null,
  )
  const [isRepeatingVisible, setIsRepeatingVisible] = useState(
    !!(currentEditCost?.max_iterations || currentEditCost?.date_end),
  )
  const [isProductResetting, setIsProductResetting] = useState(false)
  const [iterationsType, setIterationsType] = useState(ITERATION_TYPES.number)
  const [selectedProduct, setSelectedProduct] =
    useState<IndirectCostType | null>(null)
  const [costTypeValue, setCostTypeValue] = useState<string>("")
  const [costTypeError, setCostTypeError] = useState<string | null>(null)

  const form = useForm<IndirectCostType>({
    defaultValues,
  })

  const {
    handleSubmit,
    setValue,
    formState: { isSubmitting, errors, isDirty },
    reset,
    setError,
    clearErrors,
    getValues,
  } = form

  const formValues = getValues()

  const [
    frequency,
    seller_id,
    product_id,
    marketplace_id,
    max_iterations,
    date_end,
    currency_id,
    cron_expr,
  ] = useWatch({
    name: [
      PRODUCT_FIELDS.frequency,
      PRODUCT_FIELDS.seller_id,
      PRODUCT_FIELDS.product_id,
      PRODUCT_FIELDS.marketplace_id,
      PRODUCT_FIELDS.max_iterations,
      PRODUCT_FIELDS.date_end,
      PRODUCT_FIELDS.currency_id,
      PRODUCT_FIELDS.cron_expr,
    ],
    control: form.control,
  })

  const isOneTimeFrequency = frequency === FREQUENCY_TYPES.onetime
  const hasErrorsFields = checkIsArray(getObjectKeys(errors))

  const handleChangeValue = useCallback(
    (name: IndirectCostKeysTypes) =>
      (value: ProductValuesTypes): void => {
        setSelectedFieldName(name)
        setValue(name, value)
        clearErrors(name)
      },
    [clearErrors, setValue],
  )

  const handleFrequencyChange = (value: string): void => {
    const isRecurringFrequency = value === FREQUENCY_TYPES.recurring

    if (!isRecurringFrequency) {
      setValue(PRODUCT_FIELDS.cron_expr, null)
    }

    if (isRecurringFrequency) {
      setValue(PRODUCT_FIELDS.cron_expr, cron_expr || DEFAULT_CRON_EXPR)
    }

    handleChangeValue(PRODUCT_FIELDS.frequency)(value)
  }

  const handleNewCostTypeChange = (value: string): void => {
    setCostTypeError(null)
    setCostTypeValue(value)
  }

  const handleDateSelect =
    (fieldName: IndirectCostKeysTypes) =>
    (date: moment.Moment): void => {
      const formattedDate = date
        ? moment(date).format(SERVER_DATE_FORMAT)
        : null

      handleChangeValue(fieldName)(formattedDate)
    }

  const handleProductSelectFocus = (): void => {
    setIsProductResetting(false)
  }

  const handleProductSelect = (selectedProduct: IndirectCostType): void => {
    if (!selectedProduct) {
      return
    }

    const { id, seller_id: selectedSellerId, marketplace_id } = selectedProduct

    setSelectedProduct(selectedProduct)
    handleChangeValue(PRODUCT_FIELDS.product_id)(id)
    handleChangeValue(PRODUCT_FIELDS.seller_id)(selectedSellerId)
    handleChangeValue(PRODUCT_FIELDS.marketplace_id)(marketplace_id)
  }

  const handleAccountSelectChange = (value: string): void => {
    handleChangeValue(PRODUCT_FIELDS.seller_id)(value)

    const hasSellerMarketplace = !accountMarketplaces?.[value]?.find(
      ({ id }) => id === marketplace_id,
    )
    const isGlobalAccount = value === GLOBAL_SELLER
    const isNeedToResetMarketplaceSelect =
      hasSellerMarketplace || isGlobalAccount

    if (isNeedToResetMarketplaceSelect) {
      handleChangeValue(PRODUCT_FIELDS.marketplace_id)(null)
    }
  }

  const handleCostTypeDelete =
    (costId: string) =>
    (event: React.SyntheticEvent): void => {
      event.preventDefault()
      event.stopPropagation()

      dispatch(
        deleteIndirectCostType(costId, () => {
          handleChangeValue(PRODUCT_FIELDS.indirect_cost_type_id)(null)
          setCostTypeValue("")
        }),
      )
    }

  const handleNewCostTypeAdd = (): void => {
    dispatch(
      createIndirectCostType(
        { name: costTypeValue.trim() },
        () => {
          setCostTypeError(null)
          setCostTypeValue("")
        },
        (error: ErrorTypes) => {
          setCostTypeError(error?.[0]?.message || null)
        },
      ),
    )
  }

  const handleRepeatingSectionChange = (isVisible: boolean): void => {
    setIsRepeatingVisible(isVisible)

    if (!isVisible) {
      handleChangeValue(PRODUCT_FIELDS.date_end)(null)
    }
  }

  const handleIterationsTypeChange = (value: string): void => {
    setIterationsType(value)
  }

  const handleChangeCurrency = ({ id }: IndirectCostType): void => {
    handleChangeValue(PRODUCT_FIELDS.currency_id)(id)
  }

  const closeAndResetForm = (): void => {
    dispatch(setCostModalVisibility({ isVisible: false }))

    reset(defaultValues)
    clearErrors()
  }

  const handleModalClose = useCallback((): void => {
    if (isDirty) {
      setConfirm({
        title: l("Are you sure you want to proceed?"),
        message: l(`Your changes will not be saved`),
        cancelText: l("Cancel"),
        okText: l("Confirm"),
        onOk: () => {
          closeAndResetForm()
        },
      })

      return
    }

    closeAndResetForm()
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isDirty])

  const handleErrors = (errors: ErrorTypes): void => {
    if (!errors) {
      return
    }

    if (checkIsArray(errors)) {
      errors.forEach(({ field, message }) => {
        setError(field as IndirectCostKeysTypes, { type: "custom", message })
      })
    }
  }

  const handleSubmitForm = async (data: IndirectCostType): Promise<void> => {
    const action = currentEditCost?.id ? updateIndirectCost : createIndirectCost

    return await dispatch(
      action(
        data,
        () => {
          closeAndResetForm()
        },
        handleErrors,
      ),
    )
  }

  const isGlobalAccountSelected = seller_id === GLOBAL_SELLER
  const isCostTypeAddButtonDisabled = !costTypeValue
  const isMarketplaceSelectDisabled = isGlobalAccountSelected || !seller_id
  const isRecurringFrequency = frequency === FREQUENCY_TYPES.recurring
  const isIterationTypeNumber = iterationsType === ITERATION_TYPES.number
  const hasIterationsValue = !!(max_iterations || date_end)
  const isRepeatingSectionVisible = isRepeatingVisible && isRecurringFrequency
  const isSubmitButtonDisabled = isSubmitting || hasErrorsFields

  useEffect(() => {
    const isProductSelected = product_id
    const isProductDiffToSellerId = selectedProduct?.seller_id !== seller_id
    const isProductDiffToMarketplaceId =
      selectedProduct?.marketplace_id !== marketplace_id
    const isRelatedProductFieldsDifferent =
      isProductDiffToSellerId || isProductDiffToMarketplaceId
    const isNeedProductSelectReset =
      isProductSelected && isRelatedProductFieldsDifferent

    if (isNeedProductSelectReset) {
      setIsProductResetting(true)
      setSelectedProduct(null)
      handleChangeValue(PRODUCT_FIELDS.product_id)(null)

      const isSellerIdSelected = selectedFieldName === PRODUCT_FIELDS.seller_id

      if (isSellerIdSelected) {
        handleChangeValue(PRODUCT_FIELDS.marketplace_id)(null)
      }
    }
  }, [
    selectedFieldName,
    marketplace_id,
    product_id,
    seller_id,
    handleChangeValue,
    selectedProduct,
  ])

  useEffect(() => {
    const isRecurringWithInteractions =
      hasIterationsValue && isRecurringFrequency

    if (isRecurringWithInteractions) {
      setIterationsType(
        max_iterations ? ITERATION_TYPES.number : ITERATION_TYPES.date,
      )
    }
  }, [hasIterationsValue, isRecurringFrequency, max_iterations, setValue])

  useEffect(() => {
    dispatch(getIndirectCostTypes())
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [])

  return {
    form,
    formValues,
    handleSubmit,
    setValue,
    reset,
    errors,
    setCostTypeValue,
    handleChangeValue,
    handleSubmitForm,
    handleProductSelect,
    handleNewCostTypeChange,
    handleProductSelectFocus,
    handleNewCostTypeAdd,
    handleDateSelect,
    handleCostTypeDelete,
    handleRepeatingSectionChange,
    handleIterationsTypeChange,
    handleModalClose,
    handleChangeCurrency,
    handleAccountSelectChange,
    handleFrequencyChange,
    isSubmitting,
    isGlobalAccountSelected,
    isCostTypeAddButtonDisabled,
    isMarketplaceSelectDisabled,
    isProductResetting,
    costTypeError,
    costTypeValue,
    isOneTimeFrequency,
    isRepeatingSectionVisible,
    isRepeatingVisible,
    isRecurringFrequency,
    iterationsType,
    isIterationTypeNumber,
    hasIterationsValue,
    isSubmitButtonDisabled,
    currency_id,
  }
}
