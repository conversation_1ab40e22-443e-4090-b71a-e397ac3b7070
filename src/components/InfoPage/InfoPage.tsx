import React from "react"
import { Box, Typography } from "@develop/fe-library"

import l from "utils/intl"

import { PAGE_TYPES_MAPPER } from "./componentsMapper"

import { InfoPageProps } from "./InfoPageTypes"

import styles from "./infoPage.module.scss"

export const InfoPage = ({
  type = "permissionView",
  title = "",
  description = "",
}: InfoPageProps) => {
  const pageOptions = PAGE_TYPES_MAPPER[type as keyof typeof PAGE_TYPES_MAPPER]

  return (
    <Box
      padding="m"
      flexDirection="column-reverse"
      justify="center"
      margin="auto"
      minHeight="calc(100% - 60px)" // 60px is the height of the footer
      tb={{
        padding: "l",
        gap: "l",
        align: "center",
      }}
      dSM={{
        flexDirection: "row",
        gap: "xl",
      }}
      dMD={{
        maxWidth: 1480,
      }}
    >
      <Box
        gap="l"
        align="center"
        justify="center"
        flexDirection="column"
        tb={{ align: "flex-start", width: "100%" }}
      >
        {pageOptions?.icon ? pageOptions?.icon : null}
        <Typography
          variant="--font-headline-2"
          textAlign="center"
          tb={{
            variant: "--font-headline-1",
            textAlign: "left",
          }}
        >
          {l(title)}
        </Typography>
        <Typography
          variant="--font-headline-3"
          color="--color-text-second"
          textAlign="center"
          dSM={{
            variant: "--font-headline-2",
            textAlign: "left",
          }}
        >
          {l(description)}
        </Typography>
      </Box>
      {pageOptions?.image ? (
        <Box
          display="none"
          tb={{
            display: "flex",
            align: "center",
            width: 500,
          }}
          dSM={{
            flexShrink: 0,
          }}
          dMD={{
            width: 700,
          }}
        >
          <img
            src={pageOptions.image}
            alt={l(title)}
            className={styles.image}
          />
        </Box>
      ) : null}
    </Box>
  )
}
