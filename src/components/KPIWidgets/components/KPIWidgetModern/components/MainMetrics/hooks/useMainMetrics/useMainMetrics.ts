import { useMemo } from "react"

import { useSubscription } from "hooks/useSubscription"
import { useUrlParams } from "hooks/useUrlParams"

import { DashboardFiltersParams } from "types"

import { UseMainMetricsProps } from "./useMainMetricsTypes"
import { MainMetricsType } from "../../MainMetricsTypes"

export const useMainMetrics = ({ data }: UseMainMetricsProps) => {
  const { urlParams } = useUrlParams<DashboardFiltersParams>()
  const { hasFreemiumRestrictions } = useSubscription()

  const mainMetrics: MainMetricsType = useMemo(() => {
    const mainMetrics: MainMetricsType = [
      {
        icon: "icnShopping",
        name: "Product sales",
        amount: data?.amounts?.ordered_product_sales ?? 0,
        id: "ordered_product_sales",
        type: "money",
      },
      {
        icon: "icnArrowRight",
        name: "Revenue",
        amount: data?.amounts?.revenue ?? 0,
        id: "revenue",
        type: "money",
      },
      {
        icon: "icnNotification",
        name: "Ads (PPC)",
        amount: data?.amounts?.ppc ?? 0,
        id: "net_profit",
        type: "money",
        shouldBlur: hasFreemiumRestrictions,
      },
      {
        icon: "icnRise",
        name: "Estimated margin",
        amount: data?.amounts?.net_profit ?? 0,
        id: "ppc",
        type: "count",
      },
    ]

    return mainMetrics
  }, [
    data?.amounts?.net_profit,
    data?.amounts?.ordered_product_sales,
    data?.amounts?.ppc,
    data?.amounts?.revenue,
    hasFreemiumRestrictions,
  ])

  return {
    mainMetrics,
    urlParams,
  }
}
