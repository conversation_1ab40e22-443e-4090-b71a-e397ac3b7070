import { useCallback } from "react"
import { ROUTES } from "@develop/fe-library/dist/routes"
import { getUrlSearchParamsString } from "@develop/fe-library/dist/utils"
import { format } from "date-fns"

import { useGroupAccount } from "hooks"

import { DATE_FNS_FORMATS } from "constants/dateTime"

import { OrdersUrlParams, TransactionsUrlParams } from "types"

import { GetMetricUrl, UseMetricUrlProps } from "./useMetricUrlTypes"

export const useMetricUrl = ({
  urlParams,
  selectedDates,
  inputMode,
}: UseMetricUrlProps) => {
  const { getOldSellerGroupId } = useGroupAccount()

  const getMetricUrl: GetMetricUrl = useCallback(
    (additionalUrlParams) => {
      const params: TransactionsUrlParams | OrdersUrlParams = {
        ...getOldSellerGroupId(urlParams.productSeller || urlParams.seller_id),
        customerID: urlParams.customerID,
        currency_id: urlParams.currency_code,
        from: format(selectedDates.start, DATE_FNS_FORMATS.SERVER),
        to: format(selectedDates.end, DATE_FNS_FORMATS.SERVER),
        inputMode: inputMode,
        seller_sku: urlParams.productSku || urlParams.sku,
        marketplace_id: urlParams.productMarketplace,
        marketplaces: urlParams.marketplace_id,

        product_asin: urlParams.productASIN || urlParams.asin,
        product_brand: urlParams.brand,
        product_type: urlParams.product_type,
        product_manufacturer: urlParams.manufacturer,
        product_stock_type: urlParams.stock_type,
        offer_type: urlParams.offer_type,
        ...additionalUrlParams,
      }

      return (
        ROUTES.BAS_ROUTES.PATH_BAS_TRANSACTIONS +
        getUrlSearchParamsString({
          params,
        })
      )
    },
    [
      getOldSellerGroupId,
      urlParams.productSeller,
      urlParams.seller_id,
      urlParams.customerID,
      urlParams.currency_code,
      urlParams.productSku,
      urlParams.sku,
      urlParams.productMarketplace,
      urlParams.marketplace_id,
      urlParams.productASIN,
      urlParams.asin,
      urlParams.brand,
      urlParams.product_type,
      urlParams.manufacturer,
      urlParams.stock_type,
      urlParams.offer_type,
      selectedDates.start,
      selectedDates.end,
      inputMode,
    ],
  )

  return { getMetricUrl }
}
