import {
  KeyPerformance,
  KeyPerformanceProfitBreakdownExpenses,
  KeyPerformanceProfitBreakdownGeneral,
  KeyPerformanceProfitBreakdownOtherIncome,
  KeyPerformanceProfitBreakdownProductSales,
} from "types"

export type UseProfitBreakdownProps = {
  data: KeyPerformance | null
}

export type ProfitBreakdownGeneral =
  | KeyPerformanceProfitBreakdownProductSales
  | KeyPerformanceProfitBreakdownOtherIncome
  | KeyPerformanceProfitBreakdownGeneral
  | KeyPerformanceProfitBreakdownExpenses

export type ProfitBreakdownLeftSide = [
  KeyPerformanceProfitBreakdownProductSales,
  KeyPerformanceProfitBreakdownOtherIncome,
  KeyPerformanceProfitBreakdownGeneral,
]

export type ProfitBreakdownRightSide = [KeyPerformanceProfitBreakdownExpenses]
