import { useCallback, useRef } from "react"
import { BoxProps } from "@develop/fe-library"
import { useOutsideClick } from "@develop/fe-library/dist/hooks"

import { UseKPIWidgetModernUIProps } from "./useKPIWidgetModernUITypes"

export const useKPIWidgetModernUI = ({
  isOpenPopover,
  onClosePopover,
  onOpenPopover,
  index,
  isSelected,
}: UseKPIWidgetModernUIProps) => {
  const ref = useRef<HTMLDivElement>(null)

  useOutsideClick({
    refs: [ref],
    callback: onClosePopover,
    triggerEvent: "click",
    isEnabled: isOpenPopover,
  })

  const handleTogglePopover = useCallback((): void => {
    if (isOpenPopover) {
      onClosePopover()
    } else {
      onOpenPopover(index)
    }
  }, [isOpenPopover, onClosePopover, onOpenPopover, index])

  const getBoxShadow = (): BoxProps["boxShadow"] => {
    if (isOpenPopover) {
      return "--box-shadow"
    }

    if (isSelected) {
      return "--button-box-shadow-focus"
    }

    return undefined
  }

  return {
    ref,
    handleTogglePopover,
    getBoxShadow,
  }
}
