import { InputMode } from "@develop/fe-library"

import { INPUT_MODE } from "constants/dateRange"

import { DashboardFiltersParamsKeysType } from "types"

export const KPI_WIDGET_SELECTED_URL_PARAMS_TO_WATCH: DashboardFiltersParamsKeysType[] =
  [
    "seller_id",
    "marketplace_id",
    "offer_type",
    "currency_code",
    "view",
    "productASIN",
    "productMarketplace",
    "productSeller",
    "productSku",
    "asin",
    "sku",
    "upc",
    "ean",
    "isbn",
    "brand",
    "manufacturer",
    "product_type",
    "stock_type",
    "parent_asin",
    "tags",
    "adult_product",
  ]

export const KPIWIDGET_LIST_DEFAULT: InputMode[] = [
  INPUT_MODE.TODAY,
  INPUT_MODE.YESTERDAY,
  INPUT_MODE.CURRENT_MONTH,
  INPUT_MODE.LAST_MONTH,
]
