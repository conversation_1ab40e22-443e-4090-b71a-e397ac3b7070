import React from "react"
import { Box, SimpleTable, StatusTag, Typography } from "@develop/fe-library"

import { ISSUES_STATUSES } from "components/OperationalDashboard/constants"
import LinkView from "components/shared/Link"

import l from "utils/intl"

import { useCalculationAccuracyTable } from "./hooks"

export const CalculationAccuracyTable = () => {
  const { dataCompleteness, isDataCompletenessLoading, actionColumnHandler } =
    useCalculationAccuracyTable()

  return (
    <SimpleTable
      data={dataCompleteness}
      hasOuterBorder={false}
      isLoading={isDataCompletenessLoading}
      loadingRowsCount={10}
      columns={[
        {
          title: l("Issue type"),
          key: "title",
          dataIndex: "title",
          width: 170,
          renderCell: ({ value }) => {
            return (
              <Box justify="flex-start" width="100%">
                <Typography textAlign="left" variant="--font-service-text-1">
                  {l(value)}
                </Typography>
              </Box>
            )
          },
        },
        {
          title: l("Cases"),
          key: "count_unfilled",
          dataIndex: "count_unfilled",
          width: 120,
        },
        {
          title: l("Priority"),
          key: "importance",
          dataIndex: "importance",
          width: 50,
          renderCell: ({ value }) => {
            return (
              <StatusTag
                isFullWidth
                colorStatus={ISSUES_STATUSES[value]?.colorStatus}
                name={l(ISSUES_STATUSES[value]?.name)}
              />
            )
          },
        },
        {
          title: l("Action"),
          key: "action",
          dataIndex: "action",
          width: 100,
          renderCell: ({ value, item }) => {
            const { label, url = "", action = undefined } = value

            return (
              <LinkView
                internal={false}
                rel="noopener noreferrer"
                styleType="primary"
                target="_blank"
                text={l(label)}
                type="span"
                url={!action ? url : undefined}
                variant="textSmall"
                onClick={
                  action
                    ? actionColumnHandler({
                        action,
                        additionalDataModal: {
                          fill_percentage: item?.fill_percentage,
                        },
                      })
                    : undefined
                }
              />
            )
          },
        },
      ]}
    />
  )
}
