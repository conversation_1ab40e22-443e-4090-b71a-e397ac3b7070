import React from "react"
import { useSelector } from "react-redux"

import { operationalDashboardActions } from "actions/operationalDashboardActions"

import { operationalDashboardAdditionalDataModalSelector } from "selectors/operationalDashboardSelectors/operationalDashboardSelectors"

import l from "utils/intl"

import { CommonFillPercentageModal } from "../CommonFillPercentageModal"

import { DataReassemblyModalProps } from "./DataReassemblyModalTypes"

const { openDataReassemblyModal } = operationalDashboardActions

export const DataReassemblyModal = ({
  isVisibleDataReassemblyModal,
}: DataReassemblyModalProps) => {
  const additionalDataModal = useSelector(
    operationalDashboardAdditionalDataModalSelector,
  )

  return (
    <CommonFillPercentageModal
      isVisible={isVisibleDataReassemblyModal}
      modalTitle={l("Data reassembly")}
      modalText={l("{fillPercentage} of data has been reassembled", {
        fillPercentage: `${additionalDataModal.fill_percentage}%`,
      })}
      onClose={openDataReassemblyModal}
    />
  )
}
