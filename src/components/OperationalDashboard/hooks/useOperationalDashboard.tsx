import { useEffect } from "react"
import { useDispatch, useSelector } from "react-redux"

import { operationalDashboardActions } from "actions/operationalDashboardActions"

import {
  operationalDashboardIsVisibleAdjustmentToFeesDrawerSelector,
  operationalDashboardIsVisibleDataReassemblyModalSelector,
  operationalDashboardIsVisibleFbaFulfillmentFeeChangesDrawerSelector,
  operationalDashboardIsVisibleHistoricalDataLoadModalSelector,
  operationalDashboardIsVisibleReferralFeeChangesDrawerSelector,
} from "selectors/operationalDashboardSelectors/operationalDashboardSelectors"

const { clearOperationalDashboardData } = operationalDashboardActions

export const useOperationalDashboard = () => {
  const isVisibleDataReassemblyModal = useSelector(
    operationalDashboardIsVisibleDataReassemblyModalSelector,
  )
  const isVisibleHistoricalDataLoadModal = useSelector(
    operationalDashboardIsVisibleHistoricalDataLoadModalSelector,
  )
  const isVisibleReferralFeeChangesDrawer = useSelector(
    operationalDashboardIsVisibleReferralFeeChangesDrawerSelector,
  )
  const isVisibleFbaFulfillmentFeeChangesDrawer = useSelector(
    operationalDashboardIsVisibleFbaFulfillmentFeeChangesDrawerSelector,
  )
  const isVisibleAdjustmentToFeesDrawer = useSelector(
    operationalDashboardIsVisibleAdjustmentToFeesDrawerSelector,
  )

  const dispatch = useDispatch()

  useEffect(() => {
    return () => {
      dispatch(clearOperationalDashboardData())
    }
  }, [])

  return {
    isVisibleDataReassemblyModal,
    isVisibleHistoricalDataLoadModal,
    isVisibleReferralFeeChangesDrawer,
    isVisibleFbaFulfillmentFeeChangesDrawer,
    isVisibleAdjustmentToFeesDrawer,
  }
}
