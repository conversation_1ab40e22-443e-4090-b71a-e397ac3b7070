import type { ReactNode } from "react"
import type {
  TableBaseData,
  TableEntryParams,
} from "@develop/fe-library"
import type {
  FilterPaginationValues,
  FilterSortValues,
} from "@develop/fe-library/dist/lib/types"

import type {
  DashboardFiltersParams,
  OrdersUrlParams,
} from "types"
import type { AmazonOrderExtendedViewItemV1 } from "types/Models/AmazonOrderExtendedViewItem"
import type { AmazonOrderV1RequestParams } from "types/RequestParams/AmazonOrderV1RequestParams"

export type OrderItemsTableNewData = AmazonOrderExtendedViewItemV1 &
  TableBaseData

export type OrderItemsTableNewEntryParams = TableEntryParams<
  AmazonOrderExtendedViewItemV1 & TableBaseData
>

export type OrderItemsTableNewGlobalParams = {
  currency_id?: string
  date_end?: string
  date_start?: string
  seller_id?: string
  marketplace_id?: string
  marketplace_seller_ids?: string
}

export type OrderItemsTableNewFilterValues = FilterSortValues &
  FilterPaginationValues &
  Pick<
    AmazonOrderV1RequestParams,
    | "order_id"
    | "order_status"
    | "order_purchase_date"
    | "seller_id"
    | "marketplace_id"
    | "product_asin"
    | "seller_sku"
    | "product_title"
    | "product_brand"
    | "product_type"
    | "product_manufacturer"
    | "product_condition"
    | "product_stock_type"
    | "offer_type"
    | "item_price"
    | "quantity"
    | "revenue_amount"
    | "promotion_amount"
    | "amazon_fees_amount"
    | "total_expenses"
    | "total_income"
    | "estimated_profit_amount"
    | "quantity_refunded"
  > & {
    marketplace_id: Array<string>
    globalParams: OrderItemsTableNewGlobalParams
  }

export type OrderItemsTableNewProps = {
  urlSearchDefaultValue: string
}
