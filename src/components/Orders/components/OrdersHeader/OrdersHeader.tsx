import React from "react"
import { Box, Button, Popover, Typography } from "@develop/fe-library"

import { EditGroupButton } from "components/EditGroupButton"
import { GroupAccountSelect } from "components/GroupAccountSelect"
import {
  CurrencySelect,
  DateRangePickerSelect,
  MarketplaceSelect,
} from "components/shared/Page/Header"

import { convertToLocalDateTime } from "utils/dateConverter"
import l from "utils/intl"

import { useOrdersHeader } from "./hooks/useOrdersHeader"

import { OrdersHeaderProps } from "./OrdersHeaderTypes"

import styles from "./ordersHeader.module.scss"

export const OrdersHeader = ({ onExportGridOpen }: OrdersHeaderProps) => {
  const { lastUpdateOrdersDate, handleUpdateUrlParams, isOrderItemsEmpty } =
    useOrdersHeader()

  return (
    <>
      <Box
        flexDirection="column"
        gap="m"
        justify="space-between"
        padding="m"
        tb={{
          padding: "m l",
          flexDirection: "row",
          align: "center",
        }}
      >
        <Typography color="--color-text-main" variant="--font-headline-3">
          {l("Orders")}
        </Typography>

        {lastUpdateOrdersDate ? (
          <Typography color="--color-text-second" variant="--font-body-text-9">
            {l("Last update")}: {convertToLocalDateTime(lastUpdateOrdersDate)}
          </Typography>
        ) : null}
      </Box>

      <Box
        className={styles.filterContainer}
        flexDirection="column"
        gap="m"
        padding="m"
      >
        <Box
          align="flex-start"
          className={styles.filterLeftContainer}
          flex="1"
          gap="m"
        >
          <GroupAccountSelect
            useGlobal
            onSetUrlParams={handleUpdateUrlParams}
          />

          <MarketplaceSelect onSetUrlParams={handleUpdateUrlParams} />

          <Box className={styles.datePickerContainer}>
            <DateRangePickerSelect onSetUrlParams={handleUpdateUrlParams} />
          </Box>
        </Box>

        <Box
          align="flex-start"
          className={styles.filterRightContainer}
          gap="m"
          justify="flex-end"
        >
          <CurrencySelect onSetUrlParams={handleUpdateUrlParams} />

          <EditGroupButton className={styles.editGroupsButton} />

          <EditGroupButton isMobile className={styles.editGroupsButtonMobile} />

          <Popover content={l("Export results")} placement="topRight">
            <Button
              iconOnly
              disabled={isOrderItemsEmpty}
              icon="icnUpload"
              variant="secondary"
              onClick={onExportGridOpen}
            />
          </Popover>
        </Box>
      </Box>
    </>
  )
}
