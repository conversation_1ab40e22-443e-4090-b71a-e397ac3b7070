import React from "react"
import { Typography } from "@develop/fe-library"

import l from "utils/intl"
import ln from "utils/localeNumber"
import { checkIsNullOrUndefined } from "utils/validationHelper"

import { NOT_AVAILABLE } from "constants/common"

import type { EstimatedProfitAmountCellProps } from "./EstimatedProfitAmountCellTypes"

export const EstimatedProfitAmountCell = ({
  tableEntryParams,
  currencyCode,
}: EstimatedProfitAmountCellProps) => {
  const { value } = tableEntryParams

  if (checkIsNullOrUndefined(value)) {
    return <>{l(NOT_AVAILABLE)}</>
  }

  const color = Number(value) < 0 ? "--color-text-error" : "--color-text-main"

  const formattedValue: string = ln(value, 2, {
    currency: currencyCode,
  })

  return (
    <Typography color={color} variant="--font-body-text-9">
      {formattedValue}
    </Typography>
  )
}
