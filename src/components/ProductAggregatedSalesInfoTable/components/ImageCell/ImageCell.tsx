import React from "react"
import { Box, EmptyImage } from "@develop/fe-library"

import { getProductAggregatedSalesInfoImageUrl } from "../../utils"

import type { ProductAggregatedSalesInfoTableEntryParams } from "../../ProductAggregatedSalesInfoTableTypes"

export const ImageCell = ({
  item,
}: ProductAggregatedSalesInfoTableEntryParams) => {
  const url = getProductAggregatedSalesInfoImageUrl(item)

  return (
    <Box justify="center">
      <EmptyImage height={53} url={url} width={64} />
    </Box>
  )
}
