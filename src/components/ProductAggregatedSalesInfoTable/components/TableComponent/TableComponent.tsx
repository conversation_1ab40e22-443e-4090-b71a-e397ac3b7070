import React, { useMemo } from "react"
import { useSelector } from "react-redux"
import { Box, CommonMessage, Table } from "@develop/fe-library"

import { staffCurrentUserRoleSelector } from "selectors/mainStateSelectors"

import { TABLE_MAIN_BOX_PROPS } from "components/ProductAggregatedSalesInfoTable/constants"
import {
  normalizeFilterValuesToUrl,
  parseFilterValuesFromUrl,
} from "components/ProductAggregatedSalesInfoTable/utils"

import { buildTableLabels } from "utils/buildTableLabels"
import l from "utils/intl"

import { PRODUCTS_AGGREGATED_SALES_INFO_TABLE_SETTINGS_KEY } from "constants/productAggregatedSalesInfo"
import { defaultSettings } from "constants/tableSettingsNew"
import { USER_ROLES } from "constants/user"

import { useColumns, useSearch, useTableSettings } from "../../hooks"

import type {
  ProductAggregatedSalesInfoTableData,
  ProductAggregatedSalesInfoTableFilterValues,
  ProductAggregatedSalesInfoTableProps,
} from "../../ProductAggregatedSalesInfoTableTypes"

export const TableComponent = ({
  urlSearchDefaultValue,
}: ProductAggregatedSalesInfoTableProps) => {
  const role = useSelector(staffCurrentUserRoleSelector)

  const columns = useColumns()

  const { settings, handleChangeSettings, pageSize, handleChangePageSize } =
    useTableSettings()

  const { data, totalCount, filterDefaultValues, handleSearch, isLoading } =
    useSearch({
      pageSize,
      handleChangePageSize,
    })

  const labels = useMemo(buildTableLabels, [])

  return (
    <Table<
      ProductAggregatedSalesInfoTableData,
      ProductAggregatedSalesInfoTableFilterValues
    >
      hasStickyHeader
      canSaveAsDefaultSettings={role === USER_ROLES.superAdmin}
      columns={columns}
      data={data}
      filterDefaultValues={filterDefaultValues}
      filtersPrefix="products"
      isLoading={isLoading}
      labels={labels}
      mainBoxProps={TABLE_MAIN_BOX_PROPS}
      normalizeFilterValuesToUrl={normalizeFilterValuesToUrl}
      parseFilterValuesFromUrl={parseFilterValuesFromUrl}
      settings={settings}
      totalCount={totalCount}
      urlSearchDefaultValue={urlSearchDefaultValue}
      variant="standalone"
      noDataPlaceholder={
        <Box height={300} justify="center" width="100%">
          <CommonMessage icon="icnInbox" title={l("No data")} />
        </Box>
      }
      tableContentDefaultSettings={
        defaultSettings[PRODUCTS_AGGREGATED_SALES_INFO_TABLE_SETTINGS_KEY]
      }
      onChangeSettings={handleChangeSettings}
      onSearch={handleSearch}
    />
  )
}
