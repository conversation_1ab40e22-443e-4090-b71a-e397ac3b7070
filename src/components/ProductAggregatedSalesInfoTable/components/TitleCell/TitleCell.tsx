import React from "react"
import { Box, Ellipsis } from "@develop/fe-library"

import type { ProductAggregatedSalesInfoTableEntryParams } from "../../ProductAggregatedSalesInfoTableTypes"

export const TitleCell = ({
  value,
}: ProductAggregatedSalesInfoTableEntryParams) => {
  return (
    <Box width="100%">
      <Ellipsis
        rows={3}
        width="100%"
        typographyProps={{
          variant: "--font-body-text-9",
          textAlign: "left",
        }}
      >
        {value}
      </Ellipsis>
    </Box>
  )
}
