import type { BoxProps } from "@develop/fe-library"

import type { OrderStatus, OrdersUrlParams, TransactionsUrlParams } from "types"

const ORDER_STATUS_COMMON_KEYS: OrderStatus[] = [
  "Pending",
  "Unshipped",
  "PartiallyShipped",
  "Shipped",
  "Unfulfillable",
  "InvoiceUnconfirmed",
  "PendingAvailability",
] as const

export const ORDERS_URL_PARAMS_MAP: Record<string, OrdersUrlParams> = {
  units: {
    order_status: ORDER_STATUS_COMMON_KEYS,
  },
  orders: {
    order_status: ORDER_STATUS_COMMON_KEYS,
  },
  refunds: {
    quantity_refunded: ">0",
  },
}

export const TRANSACTIONS_URL_PARAMS_MAP: Record<
  string,
  TransactionsUrlParams
> = {
  revenue_amount: {
    kpi_type: ["revenue"],
  },
  total_income: {
    kpi_type: ["total_income"],
  },
  expenses_amount: {
    kpi_type: ["total_expenses"],
  },
  amazon_fees: {
    kpi_type: ["amazon_fees"],
  },
} as const

export const FILTER_BASE_INPUT_PROPS = {
  hasClearIcon: true,
} as const

export const TABLE_MAIN_BOX_PROPS: BoxProps = {
  height: 640,
  maxHeight: 640,
}
