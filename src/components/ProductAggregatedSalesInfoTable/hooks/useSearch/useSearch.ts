import { useCallback, useMemo } from "react"
import { DeepPartial } from "react-hook-form"
import { useDispatch, useSelector } from "react-redux"
import { useHistory } from "react-router-dom"
import type { TableSearchParams } from "@develop/fe-library"
import { removeNullAndUndefined } from "@develop/fe-library/dist/utils"

import { productAggregatedSalesInfoActions } from "actions/productAggregatedSalesInfoActions"

import { productAggregatedSalesInfoTableStatesSelector } from "selectors/productAggregatedSalesInfoSelectors/productAggregatedSalesInfoSelectors"

import type { ProductAggregatedSalesInfoTableFilterValues } from "components/ProductAggregatedSalesInfoTable/ProductAggregatedSalesInfoTableTypes"

import { useSellerMarketplaceParams } from "hooks"

import { checkIsArray } from "utils/arrayHelpers"

import {
  PAGE_DEFAULT,
  PAGE_SIZE_DEFAULT,
  SORT_DEFAULT,
} from "constants/tableSettingsNew"

import type { GetProductAggregatedSalesInfosRequestParams } from "types/RequestParams/GetProductAggregatedSalesInfosRequestParams"

const { getProductAggregatedSalesInfo } = productAggregatedSalesInfoActions

export const useSearch = ({ pageSize, handleChangePageSize }) => {
  const dispatch = useDispatch()
  const history = useHistory()

  const { data, totalCount, isLoading } = useSelector(
    productAggregatedSalesInfoTableStatesSelector,
  )

  const { getSellerMarketplaceParams } = useSellerMarketplaceParams()

  const filterDefaultValues: DeepPartial<ProductAggregatedSalesInfoTableFilterValues> =
    useMemo(
      () => ({
        page: PAGE_DEFAULT,
        pageSize: pageSize || PAGE_SIZE_DEFAULT,
        sort: "-units",
      }),
      [pageSize],
    )

  const handleSearch = useCallback(
    ({
      params,
      searchString,
    }: TableSearchParams<ProductAggregatedSalesInfoTableFilterValues>): void => {
      history.push({
        ...history.location,
        search: searchString,
      })

      const { globalParams = {}, ...rest } = params

      const sellerId = rest.seller_id || globalParams.seller_id
      const marketplaceId = rest.marketplace_id
        ? rest.marketplace_id.join(",")
        : globalParams.marketplace_id

      const { seller_id, marketplace_id, marketplaceSellerIds } =
        getSellerMarketplaceParams({
          ...globalParams,
          seller_id: sellerId,
          marketplace_id: checkIsArray(marketplaceId)
            ? marketplaceId.join(",")
            : marketplaceId,
        })

      const requestParams: GetProductAggregatedSalesInfosRequestParams =
        removeNullAndUndefined<GetProductAggregatedSalesInfosRequestParams>({
          page: PAGE_DEFAULT,
          pageSize: PAGE_SIZE_DEFAULT,
          sort: SORT_DEFAULT,
          ...rest,
          ...globalParams,
          product_asin: globalParams.product_asin || rest.product_asin,
          product_brand: globalParams.product_brand || rest.product_brand,
          product_manufacturer:
            globalParams.product_manufacturer || rest.product_manufacturer,
          product_type: globalParams.product_type || rest.product_type,
          seller_sku: globalParams.seller_sku || rest.seller_sku,
          seller_id,
          marketplace_seller_ids: marketplaceSellerIds,
          marketplace_id,
        })

      const successCallback = (): void => {
        // Update the page size in table settings
        handleChangePageSize(params.pageSize)
      }

      dispatch(
        getProductAggregatedSalesInfo({
          params: requestParams,
          successCallback,
        }),
      )
    },
    [getSellerMarketplaceParams, handleChangePageSize],
  )

  return {
    data,
    totalCount,
    filterDefaultValues,
    handleSearch,
    isLoading,
  }
}
