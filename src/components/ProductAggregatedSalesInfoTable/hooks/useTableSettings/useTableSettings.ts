import { useCallback } from "react"
import { useDispatch, useSelector } from "react-redux"
import type { OnChangeTableSettingsParams } from "@develop/fe-library"

import { productAggregatedSalesInfoActions } from "actions/productAggregatedSalesInfoActions"

import { productAggregatedSalesInfoTableSettingsSelector } from "selectors/productAggregatedSalesInfoSelectors/productAggregatedSalesInfoSelectors"

import { checkIsFunction } from "utils/validationHelper"

const { getTableSettings, updateTableSettings, updateDefaultTableSettings } =
  productAggregatedSalesInfoActions

export const useTableSettings = () => {
  const dispatch = useDispatch()

  // Destructuring below causes error even though selector has a return type.
  // Possibly, it happens only for selector combining multiple selectors
  // TODO: Fix selector typing
  // ts-expect-error
  const { settings, pageSize } = useSelector(
    productAggregatedSalesInfoTableSettingsSelector,
  )

  const handleChangeSettings = useCallback(
    ({
      actionType,
      closeModal,
      settings: settingsNew,
    }: OnChangeTableSettingsParams): void => {
      const payload = {
        settings: settingsNew,
        pageSize,
      }

      const successCallback = () => {
        dispatch(getTableSettings({}))

        if (checkIsFunction(closeModal)) {
          closeModal()
        }
      }

      switch (actionType) {
        case "saveSettingsAsDefault": {
          dispatch(updateDefaultTableSettings({ payload, successCallback }))
          break
        }
        case "resetSettingsToDefault": {
          dispatch(updateTableSettings({ payload, successCallback }))
          break
        }
        case "saveContent": {
          dispatch(
            updateTableSettings({ payload, successCallback: closeModal }),
          )
          break
        }
        case "saveSettings": {
          dispatch(updateTableSettings({ payload }))
          break
        }
        default:
          break
      }
    },
    [pageSize],
  )

  const handleChangePageSize = useCallback(
    (pageSizeNew: number) => {
      if (pageSizeNew === pageSize) {
        return
      }

      const payload = { pageSize: pageSizeNew }

      dispatch(
        updateTableSettings({
          payload,
        }),
      )
    },
    [pageSize],
  )

  return {
    settings,
    handleChangeSettings,
    pageSize,
    handleChangePageSize,
  }
}
