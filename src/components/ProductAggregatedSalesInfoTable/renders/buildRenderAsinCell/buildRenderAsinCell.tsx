import React from "react"

import { AsinCell } from "../../components"

import type { AmazonMarketplace } from "types/Models/AmazonMarketplace"

import type { ProductAggregatedSalesInfoTableEntryParams } from "../../ProductAggregatedSalesInfoTableTypes"

export const buildRenderAsinCell =
  (amazonMarketplaces: Array<AmazonMarketplace>) =>
  (tableEntryParams: ProductAggregatedSalesInfoTableEntryParams) => {
    return (
      <AsinCell
        amazonMarketplaces={amazonMarketplaces}
        tableEntryParams={tableEntryParams}
      />
    )
  }
