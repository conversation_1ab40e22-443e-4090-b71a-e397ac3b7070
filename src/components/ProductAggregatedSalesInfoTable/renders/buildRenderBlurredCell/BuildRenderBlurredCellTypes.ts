import { ReactNode } from "react"

import type { ProductAggregatedSalesInfoTableEntryParams } from "components/ProductAggregatedSalesInfoTable/ProductAggregatedSalesInfoTableTypes"

type BuildRenderBlurredCellParams = {
  renderValue: (
    tableEntryParams: ProductAggregatedSalesInfoTableEntryParams,
  ) => ReactNode
  label: string
  shouldBlur: boolean
}

export type BuildRenderBlurredCell = (
  params: BuildRenderBlurredCellParams,
) => (tableEntryParams: ProductAggregatedSalesInfoTableEntryParams) => ReactNode
