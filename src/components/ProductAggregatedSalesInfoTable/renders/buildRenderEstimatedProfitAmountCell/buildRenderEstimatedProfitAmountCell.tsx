import React from "react"

import { EstimatedProfitAmountCell } from "../../components/EstimatedProfitAmountCell"

import type { ProductAggregatedSalesInfoTableEntryParams } from "../../ProductAggregatedSalesInfoTableTypes"
import type { BuildRenderEstimatedProfitAmountCellProps } from "./BuildRenderEstimatedProfitAmountCellTypes"

export const buildRenderEstimatedProfitAmountCell = ({
  currencyCode,
}: BuildRenderEstimatedProfitAmountCellProps) => {
  return (tableEntryParams: ProductAggregatedSalesInfoTableEntryParams) => {
    return (
      <EstimatedProfitAmountCell
        currencyCode={currencyCode}
        tableEntryParams={tableEntryParams}
      />
    )
  }
}
