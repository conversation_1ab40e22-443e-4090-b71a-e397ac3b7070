import type { DateRangeUrlParams } from "types"

import { RenderProductAggregatedSalesInfoTableCell } from "../../ProductAggregatedSalesInfoTableTypes"

type BuildRenderOrdersLinkCellsParams = {
  dateRangeUrlParams: DateRangeUrlParams
}

type BuildRenderOrdersLinkCellsReturn = {
  renderUnitsCell: RenderProductAggregatedSalesInfoTableCell
  renderOrdersCell: RenderProductAggregatedSalesInfoTableCell
  renderRefundsCell: RenderProductAggregatedSalesInfoTableCell
}

export type BuildRenderOrdersLinkCells = (
  params: BuildRenderOrdersLinkCellsParams,
) => BuildRenderOrdersLinkCellsReturn
