import { ReactNode } from "react"

import type { AmazonCustomerAccount } from "types"

import type { ProductAggregatedSalesInfoTableEntryParams } from "../../ProductAggregatedSalesInfoTableTypes"

type BuildRenderSellerIdCellParams = {
  amazonCustomerAccounts: Array<AmazonCustomerAccount>
  shouldBlur: boolean
}

export type BuildRenderSellerIdCell = (
  params: BuildRenderSellerIdCellParams,
) => (tableEntryParams: ProductAggregatedSalesInfoTableEntryParams) => ReactNode
