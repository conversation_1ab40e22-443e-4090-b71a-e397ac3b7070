import l from "utils/intl"

import { NOT_AVAILABLE } from "constants/common"

import type { ProductAggregatedSalesInfoTableEntryParams } from "../../ProductAggregatedSalesInfoTableTypes"

export const getAsinFormatted = (
  tableEntryParams: ProductAggregatedSalesInfoTableEntryParams,
): string => {
  const { item } = tableEntryParams
  const { product_asin } = item

  if (!product_asin) {
    return l(NOT_AVAILABLE)
  }

  return product_asin.toLocaleUpperCase()
}
