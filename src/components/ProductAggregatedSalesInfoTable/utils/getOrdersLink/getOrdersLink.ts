import { ROUTES } from "@develop/fe-library/dist/routes"
import { getUrlSearchParamsString } from "@develop/fe-library/dist/utils"

import type { OrdersUrlParams } from "types"

import type { GetOrderLink } from "./GetOrdersLinkTypes"

export const getOrdersLink: GetOrderLink = ({
  productAggregatedSalesInfo,
  ordersUrlParams,
}) => {
  const searchParams = getUrlSearchParamsString<OrdersUrlParams>({
    params: {
      ...ordersUrlParams,

      marketplace_id: productAggregatedSalesInfo.marketplace_id,
      seller_sku: `=${productAggregatedSalesInfo.seller_sku}`,
      seller_id: productAggregatedSalesInfo.seller_id,
    },
  })

  return `${ROUTES.BAS_ROUTES.PATH_BAS_ORDERS}${searchParams}`
}
