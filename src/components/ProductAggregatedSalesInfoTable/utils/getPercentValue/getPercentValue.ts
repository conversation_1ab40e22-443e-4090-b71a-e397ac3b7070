import l from "utils/intl"
import { checkIsNullOrUndefined } from "utils/validationHelper"

import { NOT_AVAILABLE } from "constants/common"

import type { ProductAggregatedSalesInfoTableEntryParams } from "../../ProductAggregatedSalesInfoTableTypes"

export const getPercentValue = (
  tableEntryParams: ProductAggregatedSalesInfoTableEntryParams,
) => {
  const { value } = tableEntryParams

  if (checkIsNullOrUndefined(value)) {
    return l(NOT_AVAILABLE)
  }

  return `${value} %`
}
