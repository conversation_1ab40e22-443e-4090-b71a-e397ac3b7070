import { DATE_OUTPUT_FORMATS } from "@develop/fe-library/dist/consts"
import { ROUTES } from "@develop/fe-library/dist/routes"
import {
  formatDateRange,
  getUrlSearchParamsString,
} from "@develop/fe-library/dist/utils"
import { format } from "date-fns"

import { INPUT_MODE } from "constants/dateRange"
import { DATE_FNS_FORMATS } from "constants/dateTime"
import { PAGE_VIEW } from "constants/pageView"

import type { TransactionsUrlParams } from "types"

import type { GetTransactionsLink } from "./GetTransactionsLinkTypes"

export const getTransactionsLink: GetTransactionsLink = ({
  view,
  minStatsDate,
  today,
  productAggregatedSalesInfo,
  transactionsUrlParams,
  postedDateRangeParams,
}) => {
  const { from, to, inputMode } = postedDateRangeParams || {}

  const getPostedDateRangeFormatted = (): string => {
    if (!from || !to) {
      return ""
    }

    const range = {
      start: new Date(from),
      end: new Date(to),
    }

    return formatDateRange({
      range: range,
      format: DATE_OUTPUT_FORMATS.SERVER_DATE_FORMAT_WITH_TIME,
    })
  }

  const dateRange: TransactionsUrlParams =
    view === PAGE_VIEW.ORDER
      ? {
          from: format(minStatsDate, DATE_FNS_FORMATS.SERVER),
          to: format(today, DATE_FNS_FORMATS.SERVER),
          inputMode: INPUT_MODE.ALL_TIME,
          posted_date: getPostedDateRangeFormatted(),
        }
      : {
          from,
          to,
          inputMode,
        }

  const { seller_id, seller_sku, marketplace_id } = productAggregatedSalesInfo

  const searchParams = getUrlSearchParamsString<TransactionsUrlParams>({
    params: {
      ...dateRange,
      ...transactionsUrlParams,
      seller_id: seller_id,
      sku: `=${seller_sku}`,
      marketplace_id: marketplace_id,
    },
  })

  return `${ROUTES.BAS_ROUTES.PATH_BAS_TRANSACTIONS}${searchParams}`
}
