import { useCallback, useMemo } from "react"
import { useDispatch, useSelector } from "react-redux"
import { useForm } from "react-hook-form"
import { addDays, format } from "date-fns"
import { DATE_OUTPUT_FORMATS } from "@develop/fe-library/dist/consts"

import { createPeriodModalStatesSelector } from "selectors/productCostSelectors"

import productCostActions from "actions/productCostActions"

import l from "utils/intl"
import ln from "utils/localeNumber"
import { buildDatePickerLabels } from "utils/buildDatePickerLabels"

import { FORM_TYPES, FORM_TYPES_LOWER_TITLES } from "constants/productCost"
import { LOCALES } from "constants/locales"

import type { CreatePeriodModalFormValues } from "components/ProductCostDrawer/components/CreatePeriodModal/CreatePeriodModalTypes"

const { closeCreatePeriodModal, createPeriod } = productCostActions

export const useCreatePeriodModal = () => {
  const dispatch = useDispatch()

  const {
    formType,
    marketplace_id,
    sku,
    seller_id,
    currency_code,
    productCostValue,
    userTimezone,
    language,
    locale,
  } = useSelector(createPeriodModalStatesSelector)

  const form = useForm<CreatePeriodModalFormValues>({
    defaultValues: {
      date_start: null,
    },
  })

  const { handleSubmit: formHandleSubmit, watch, formState } = form

  const { isDirty, isSubmitting } = formState

  const date = watch("date_start")

  const isSubmitButtonDisabled: boolean = !date || !isDirty || isSubmitting

  const handleClose = useCallback(() => {
    dispatch(closeCreatePeriodModal())
  }, [])

  const handleSubmit = useCallback(
    formHandleSubmit(async ({ date_start }) => {
      if (!date_start) {
        return
      }

      const dateStartFormatted = format(
        date_start,
        DATE_OUTPUT_FORMATS.SERVER_DATE_FORMAT_WITH_TIME,
      )

      const payload = {
        marketplace_id,
        seller_sku: sku,
        seller_id,
        date_start: dateStartFormatted,
      }

      await dispatch(createPeriod(payload, handleClose))
    }),
    [marketplace_id, sku, seller_id, userTimezone, handleClose],
  )

  const items = useMemo(() => {
    const toDate = addDays(new Date(), -1)

    return [
      {
        type: "date",
        name: "date_start",
        inputProps: {
          label: l("Date"),
          toDate,
          timezone: userTimezone,
          labels: buildDatePickerLabels(),
          language: LOCALES[language],
          locale: LOCALES[locale],
        },
        gridItemProps: {
          mSM: 12,
        },
      },
    ]
  }, [userTimezone, language, locale])

  const canBeRendered: boolean = !!productCostValue && !!currency_code

  const productBuyingPrice: string =
    formType === FORM_TYPES.vat
      ? `${productCostValue}%`
      : ln(productCostValue, 2, {
          currency: currency_code,
        })

  const title = l(`Add new ${FORM_TYPES_LOWER_TITLES[formType]}`)

  return {
    form,
    canBeRendered,
    productBuyingPrice,
    onClose: handleClose,
    onSubmit: handleSubmit,
    isSubmitting,
    isSubmitButtonDisabled,
    items,
    title,
  }
}
