import React, { useEffect, useMemo, useRef, useState } from "react"
import { Box, Flag, Menu, TextInput } from "@develop/fe-library"
import { mouseEventWithStoppedPropagation } from "@develop/fe-library/dist/utils"

import { checkIsArray } from "utils/arrayHelpers"

import { CURRENCIES_NAMES } from "constants/currencies"

import { CurrencyMenuProps } from "./CurrencyMenuTypes"

export const CurrencyMenu = ({
  currencies,
  calculatorCurrencyId,
  handleChangeCalculatorCurrency,
  isVisible,
  onClose,
}: CurrencyMenuProps) => {
  const searchInputRef = useRef<HTMLInputElement>(null)
  const [searchValue, setSearchValue] = useState("")

  useEffect(() => {
    if (isVisible) {
      setSearchValue("")

      setTimeout(() => {
        searchInputRef.current?.focus()
      }, 100)
    }
  }, [isVisible])

  const currenciesOptionsFiltered = useMemo(() => {
    const result = []

    if (!checkIsArray(currencies)) {
      return result
    }

    currencies.forEach(({ id }) => {
      const label = CURRENCIES_NAMES[id]?.name

      const isIncluded = label.toLowerCase().includes(searchValue.toLowerCase())

      if (!isIncluded) {
        return
      }

      result.push({
        value: id,
        label,
      })
    })

    return result
  }, [currencies, searchValue])

  const selectedIndex = currenciesOptionsFiltered.findIndex(
    ({ value }) => value === calculatorCurrencyId,
  )

  const handleSelect = (index: number): void => {
    handleChangeCalculatorCurrency(currenciesOptionsFiltered[index].value)
    onClose?.()
  }

  return (
    <div
      onClick={mouseEventWithStoppedPropagation}
      onMouseDown={mouseEventWithStoppedPropagation}
    >
      <Box padding="m">
        <TextInput
          ref={searchInputRef}
          isFullWidth
          value={searchValue}
          onChange={setSearchValue}
          prefixIcons={[
            {
              name: "icnSearch",
              content: null,
            },
          ]}
        />
      </Box>

      <Menu
        items={currenciesOptionsFiltered}
        selectedIndex={selectedIndex}
        onSelect={handleSelect}
        maxVisibleItems={10}
        renderItem={({ item }) => {
          const { countryCode, name } = CURRENCIES_NAMES[item.value]
          return (
            <Box gap="m">
              <Flag
                size={18}
                locale={countryCode.toLowerCase()}
                borderRadius="--border-radius-circle"
              />
              {name}
            </Box>
          )
        }}
      />
    </div>
  )
}
