import React, {
  ReactN<PERSON>,
  useCallback,
  useEffect,
  useMemo,
  useState,
} from "react"
import { useForm } from "react-hook-form"
import { useDispatch, useSelector } from "react-redux"
import {
  But<PERSON>,
  ButtonPopover,
  DropdownWithPopover,
  Icon,
  IconPopover,
  InputGroup,
} from "@develop/fe-library"
import { useFormReset } from "@develop/fe-library/dist/hooks"
import { getObjectKeys } from "@develop/fe-library/dist/utils"
import { isValid } from "date-fns"
import isEqual from "lodash.isequal"

import productCostActions from "actions/productCostActions"

import { editCostItemFormStatesSelector } from "selectors/productCostSelectors"

import { ConnectedField } from "components/ProductCostDrawer/components/ConnectedField"
import { confirmResetChanges } from "components/ProductCostDrawer/utils"

import { checkIsArray } from "utils/arrayHelpers"
import { convertToLocalDate } from "utils/dateConverter"
import l from "utils/intl"
import ln from "utils/localeNumber"

import { DEFAULT_CURRENCY_CODE } from "constants/currencies"
import { FORM_TYPES_LOWER_TITLES } from "constants/productCost"

import { CurrencyExchangeRate, SelectCategory } from "../../components"

import { checkProductCostCategoryDuplicates, formatAmount } from "./utils"

import { NUMBER_FORMAT_OPTIONS } from "./constants"

import type { ProductCostItem } from "types/Models"

import type { Row } from "../../EditCostItemFormTypes"
import type {
  EditCostItemFormValues,
  UseEditCostItemFormParams,
} from "./UseEditCostItemFormTypes"

const { setIsFormEditing, endEditPeriod, updateProductCosts } =
  productCostActions

export const useEditCostItemForm = ({ items }: UseEditCostItemFormParams) => {
  const dispatch = useDispatch()

  const {
    formType,
    id,
    amount_total,
    date_start,
    date_end,
    currency_code,
    categories,
    isFormChanged,
    currencies,
  } = useSelector(editCostItemFormStatesSelector)

  const [rows, setRows] = useState<Array<Row>>(
    items.map((_, index) => ({ id: index })),
  )

  const [hasDuplicates, setHasDuplicates] = useState<boolean>(false)
  const [duplicateCostIds, setDuplicateCostIds] = useState<number[]>(() => {
    const { selectedProductCostCategoryIds = [] } = checkIsArray(items)
      ? checkProductCostCategoryDuplicates(items)
      : {}

    return selectedProductCostCategoryIds
  })

  const initialValues = {
    items,
    amount_total,
  }

  const {
    control,
    setValue,
    getValues,
    handleSubmit: formHandleSubmit,
    formState,
    watch,
    reset,
    setError,
  } = useForm<EditCostItemFormValues>({
    defaultValues: initialValues,
  })

  useFormReset<EditCostItemFormValues>({
    initialValues,
    reset,
  })

  useEffect(() => {
    const { unsubscribe } = watch(({ items } = {}) => {
      const {
        selectedProductCostCategoryIds = [],
        hasProductCostDuplicateIds = false,
      } = checkIsArray(items) ? checkProductCostCategoryDuplicates(items) : {}

      setHasDuplicates(hasProductCostDuplicateIds)
      setDuplicateCostIds(selectedProductCostCategoryIds)
    })

    return () => unsubscribe()
  }, [watch])

  const { isSubmitting } = formState

  const isSaveButtonDisabled: boolean =
    isSubmitting || !isFormChanged || hasDuplicates

  const duplicateErrorText: string | undefined = hasDuplicates
    ? l("Please, select a different subcategory")
    : undefined

  const amountTotalValue = watch("amount_total")
  const amountTotalFormatted = ln(amountTotalValue || 0, 2, {
    currency: currency_code,
  })

  const hasValidDateStart: boolean =
    !!date_start && isValid(new Date(date_start))

  const dateStartFormatted: ReactNode = hasValidDateStart ? (
    convertToLocalDate(date_start)
  ) : (
    <Icon name="icnInfinity" size="--icon-size-2" />
  )

  const hasValidDateEnd: boolean = !!date_end && isValid(new Date(date_end))

  const dateEndFormatted: ReactNode = hasValidDateEnd ? (
    convertToLocalDate(date_end)
  ) : (
    <Icon name="icnInfinity" size="--icon-size-2" />
  )

  const cancelButtonText: string = isFormChanged ? l("Cancel") : l("Back")

  const handleCancel = useCallback(() => {
    const callback = () => {
      dispatch(setIsFormEditing(false))
      dispatch(endEditPeriod())
    }

    confirmResetChanges({
      callback,
      isChanged: isFormChanged,
    })
  }, [isFormChanged])

  const handleSubmit = useCallback(
    formHandleSubmit(async ({ items }) => {
      const payload = {
        id,
        items,
      }

      const failureCallback = (serverErrors) => {
        const formErrors = serverErrors.reduce((acc, { index, errors }) => {
          const keyPrefix = `items.${index}`
          const keys = getObjectKeys(errors)

          if (!checkIsArray(keys)) {
            return acc
          }

          return {
            ...acc,
            ...keys.reduce((fieldErrors, fieldKey) => {
              const errorKey = `${keyPrefix}.${fieldKey}`
              const errorMessage = errors[fieldKey].join("\n")

              return {
                ...fieldErrors,
                [errorKey]: errorMessage,
              }
            }, {}),
          }
        }, {})

        getObjectKeys(formErrors).forEach((field) => {
          // @ts-expect-error
          setError(field, {
            type: "manual",
            message: formErrors[field],
          })
        })
      }

      await dispatch(
        updateProductCosts({
          payload,
          failureCallback,
        }),
      )
    }),
    [],
  )

  const columns = useMemo(() => {
    const shortFormTypeNameLower = FORM_TYPES_LOWER_TITLES[formType]

    const currenciesOptions = checkIsArray(currencies)
      ? currencies.map((item) => ({
          value: item.id,
          label: item.id,
        }))
      : []

    const defaultCostTypeCategory = categories.find(
      ({ is_default: isDefault }) => !!isDefault,
    )

    const handleChange = (): void => {
      const isChangedNew = !isEqual(items, getValues("items"))

      dispatch(setIsFormEditing(isChangedNew))
    }

    const euroToMarketplaceCurrency: number = parseFloat(
      currencies.find(({ id }) => id === currency_code)?.rate || 1,
    )

    const updateAmountTotalSum = (): void => {
      const itemsValues = getValues("items")

      const amountTotalSum = itemsValues.reduce((acc, item) => {
        let amountToAdd: number = item.amount_total
          ? parseFloat(item.amount_total)
          : 0

        if (item.marketplace_currency_rate !== null) {
          amountToAdd = amountToAdd * item.marketplace_currency_rate
        } else if (currency_code !== item.currency_id) {
          const currency = currencies.find(({ id }) => id === item.currency_id)

          const euroToItemCurrency: number = parseFloat(currency?.rate || 1)

          amountToAdd =
            (amountToAdd * euroToMarketplaceCurrency) / euroToItemCurrency
        }

        return acc + amountToAdd
      }, 0)

      setValue("amount_total", formatAmount(amountTotalSum), {
        shouldDirty: true,
        shouldTouch: true,
      })
    }

    const handleUpdateFields = (newValues: Array<ProductCostItem>): void => {
      setValue("items", newValues, {
        shouldDirty: true,
        shouldTouch: true,
      })

      setRows(newValues.map((_, index) => ({ id: index })))
      updateAmountTotalSum()
      handleChange()
    }

    const handleAddItem = (): void => {
      const values = getValues("items")

      const newItem: ProductCostItem = {
        amount_per_unit: "0.00",
        amount_total: "0.00",
        currency_id: currency_code,
        marketplace_currency_rate: null,
        note: null,
        product_cost_category_id: defaultCostTypeCategory?.id || null,
        product_cost_period_id: id,
        units: 1,
      }

      const newValues = [...values, newItem]

      handleUpdateFields(newValues)
    }

    const buildHandleRemoveItem = (targetIndex: number) => (): void => {
      const values = getValues("items")

      const newValues = values.filter((_, index) => index !== targetIndex)

      handleUpdateFields(newValues)
    }

    const buildHandleChangeCalculatedField = (index: number) => () => {
      const values = getValues("items")
      const item = values[index]

      const amountTotal = formatAmount(
        item.units * parseFloat(item.amount_per_unit),
      )

      setValue(`items.${index}.amount_total`, amountTotal, {
        shouldDirty: true,
        shouldTouch: true,
      })

      updateAmountTotalSum()
      handleChange()
    }

    const buildHandleChangeAmountTotal = (index: number) => (): void => {
      const values = getValues("items")
      const item = values[index]

      const amountPerUnit = formatAmount(
        parseFloat(item.amount_total) / item.units,
      )

      setValue(`items.${index}.amount_per_unit`, amountPerUnit, {
        shouldDirty: true,
        shouldTouch: true,
      })

      updateAmountTotalSum()
      handleChange()
    }

    const buildHandleSaveCurrencyExchangeRate =
      (index: number) =>
      ({ calculatorCurrencyId, calculatorValue }) => {
        setValue(`items.${index}.currency_id`, calculatorCurrencyId, {
          shouldDirty: true,
          shouldTouch: true,
        })

        setValue(`items.${index}.marketplace_currency_rate`, calculatorValue, {
          shouldDirty: true,
          shouldTouch: true,
        })

        updateAmountTotalSum()
        handleChange()
      }

    const buildHandleChangeCurrencyId = (index: number) => (): void => {
      setValue(`items.${index}.marketplace_currency_rate`, null, {
        shouldDirty: true,
        shouldTouch: true,
      })

      updateAmountTotalSum()
      handleChange()
    }

    const buildGetInitialValues = (index: number) => () => {
      const items = getValues("items")

      const {
        currency_id: calculatorCurrencyId,
        marketplace_currency_rate: marketplaceCurrencyRate,
      } = items[index]

      if (marketplaceCurrencyRate) {
        const calculatorValue: number | null =
          Math.floor(marketplaceCurrencyRate * 2000) / 2000

        return {
          calculatorCurrencyId,
          calculatorValue,
        }
      }

      const currencyToEuroRate: string = currencies.find(
        ({ id }) => id === calculatorCurrencyId,
      )?.rate

      const calculatorValueInEuro: number = 1 / parseFloat(currencyToEuroRate)

      if (calculatorCurrencyId === DEFAULT_CURRENCY_CODE) {
        return {
          calculatorCurrencyId,
          calculatorValue: calculatorValueInEuro,
        }
      }

      const calculatorValueInMarketplaceCurrency =
        Math.floor(calculatorValueInEuro * euroToMarketplaceCurrency * 2000) /
        2000

      return {
        calculatorCurrencyId,
        calculatorValue: calculatorValueInMarketplaceCurrency,
      }
    }

    return [
      {
        title: l("Cost type"),
        key: "product_cost_category_id",
        dataIndex: "id",
        width: 200,
        renderCell: ({ value }) => {
          const name = `items.${value}.product_cost_category_id`

          return (
            <SelectCategory
              control={control}
              duplicateCostIds={duplicateCostIds}
              name={name}
              onChange={handleChange}
            />
          )
        },
      },
      {
        title: l("Unit price"),
        key: "amount_per_unit",
        dataIndex: "id",
        width: 100,
        renderCell: ({ value }) => {
          const name = `items.${value}.amount_per_unit`

          return (
            <ConnectedField
              control={control}
              name={name}
              type="numeric"
              inputProps={{
                isFullWidth: true,
                isStringMode: true,
                isNegativeAllowed: false,
                ...NUMBER_FORMAT_OPTIONS,
                onChange: buildHandleChangeCalculatedField(value),
              }}
            />
          )
        },
      },
      {
        title: l("Quantity"),
        key: "units",
        dataIndex: "id",
        width: 60,
        renderCell: ({ value }) => {
          const name = `items.${value}.units`

          return (
            <ConnectedField
              control={control}
              name={name}
              type="numeric"
              inputProps={{
                isFullWidth: true,
                isStringMode: true,
                isDecimalAllowed: false,
                isZeroAllowed: false,
                isNegativeAllowed: false,
                onChange: buildHandleChangeCalculatedField(value),
              }}
            />
          )
        },
      },
      {
        title: l("Total"),
        key: "amount_total",
        dataIndex: "id",
        width: 100,
        renderCell: ({ value }) => {
          const name = `items.${value}.amount_total`

          return (
            <ConnectedField
              control={control}
              name={name}
              type="numeric"
              inputProps={{
                isFullWidth: true,
                isStringMode: true,
                isNegativeAllowed: false,
                ...NUMBER_FORMAT_OPTIONS,
                onChange: buildHandleChangeAmountTotal(value),
              }}
            />
          )
        },
      },
      {
        title: l("Currency"),
        key: "currency_id",
        dataIndex: "id",
        width: 100,
        renderCell: ({ value }) => {
          const name = `items.${value}.currency_id`

          return (
            <InputGroup>
              <ConnectedField
                control={control}
                name={name}
                type="select"
                inputProps={{
                  isFullWidth: true,
                  isGlobal: true,
                  options: currenciesOptions,
                  onChange: buildHandleChangeCurrencyId(value),
                }}
              />
              <DropdownWithPopover
                dropdownProps={{
                  isVisibilityPassedToMenu: true,
                  menu: (
                    <CurrencyExchangeRate
                      getInitialValues={buildGetInitialValues(value)}
                      onSave={buildHandleSaveCurrencyExchangeRate(value)}
                    />
                  ),
                }}
                popoverProps={{
                  content: l("Edit currency exchange rate"),
                }}
              >
                <Button
                  iconOnly
                  icon="icnCurrencyExchange"
                  variant="secondary"
                />
              </DropdownWithPopover>
            </InputGroup>
          )
        },
      },
      {
        title: l("Note"),
        key: "note",
        dataIndex: "id",
        width: 100,
        renderCell: ({ value }) => {
          const name = `items.${value}.note`

          return (
            <ConnectedField
              control={control}
              name={name}
              type="text"
              inputProps={{
                isFullWidth: true,
                onChange: handleChange,
              }}
            />
          )
        },
      },
      {
        title: (
          <ButtonPopover
            iconOnly
            content={l(`Add new ${shortFormTypeNameLower}`)}
            icon="icnPlus"
            variant="secondary"
            onClick={handleAddItem}
          />
        ),
        key: "actions",
        dataIndex: "id",
        width: 52,
        renderCell: ({ value }) => {
          return (
            <IconPopover
              content={l("Delete")}
              name="icnDeleteOutlined"
              placement="top"
              size="--icon-size-4"
              onClick={buildHandleRemoveItem(value)}
            />
          )
        },
      },
    ]
  }, [
    categories,
    currencies,
    currency_code,
    formType,
    id,
    items,
    setValue,
    getValues,
    duplicateCostIds,
  ])

  return {
    dateStartFormatted,
    dateEndFormatted,
    amountTotalFormatted,
    rows,
    columns,
    isSubmitting,
    isSaveButtonDisabled,
    duplicateErrorText,
    cancelButtonText,
    onSubmit: handleSubmit,
    onCancel: handleCancel,
  }
}
