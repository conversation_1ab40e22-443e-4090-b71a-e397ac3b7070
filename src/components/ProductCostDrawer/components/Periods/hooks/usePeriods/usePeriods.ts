import { useSelector } from "react-redux"
import { breakpoints } from "@develop/fe-library/dist/consts"
import { useBreakpoint } from "@develop/fe-library/dist/hooks"

import { periodsStatesSelector } from "selectors/productCostSelectors"

import { useSubscription } from "hooks"

export const usePeriods = () => {
  const {
    isProductCostsEditFormVisible,
    isSinglePeriod,
    isEmptyProductPeriods,
    isLoading,
    isEnabledSyncWithRepricer,
    periods,
    formType,
    selectedPeriodId,
    currencyCode,
    productCostValue,
    isBasSubscriptionExpired,
  } = useSelector(periodsStatesSelector)

  const { isRepricerSubscriptionActive } = useSubscription()

  const breakpoint = useBreakpoint()

  const isMobile: boolean =
    breakpoint === breakpoints.mSM ||
    breakpoint === breakpoints.mMD ||
    breakpoint === breakpoints.mLG

  const gridTemplateColumns: [string, string, string] = [
    `${isMobile ? 80 : 180}px`,
    "min-content",
    "1fr",
  ]

  return {
    isProductCostsEditFormVisible,
    isSinglePeriod,
    isEmptyProductPeriods,
    isLoading,
    isEnabledSyncWithRepricer,
    periods,
    formType,
    selectedPeriodId,
    currencyCode,
    productCostValue,
    isBasSubscriptionExpired,
    gridTemplateColumns,
    isMobile,
    isRepricerAvailable: isRepricerSubscriptionActive,
  }
}
