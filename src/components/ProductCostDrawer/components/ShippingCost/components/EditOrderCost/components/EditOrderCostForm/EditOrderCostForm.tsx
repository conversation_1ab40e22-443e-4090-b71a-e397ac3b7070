import React from "react"
import { Empty } from "antd"
import {
  Box,
  Button,
  Flag,
  InputAddon,
  InputGroup,
  SimpleTable,
  Typography,
} from "@develop/fe-library"

import l from "utils/intl"
import { checkIsArray } from "utils/arrayHelpers"

import { ConnectedField } from "components/ProductCostDrawer/components/ConnectedField"

import { useEditOrderCostForm } from "./hooks"

import type { AmazonOrderItemAmountCost } from "types/Models"
import type { EditOrderCostFormProps } from "./EditOrderCostFormTypes"

export const EditOrderCostForm = ({
  orderAmountCosts,
}: EditOrderCostFormProps) => {
  const {
    control,
    items,
    columns,
    onChangeTotal,
    onClose,
    onSubmit,
    isSubmitting,
    isSaveButtonDisabled,
    currencyId,
    flagLocale,
  } = useEditOrderCostForm({
    orderAmountCosts,
  })

  return (
    <Box flex={1} flexDirection="column">
      <Box
        maxHeight={52}
        padding="m l"
        hasBorder={{ bottom: true }}
        align="center"
        justify="space-between"
      >
        <Typography variant="--font-body-text-7">
          {l("Order shipping cost")}
        </Typography>

        <Box width="fit-content" minWidth={80} align="center" justify="center">
          <InputGroup>
            <ConnectedField
              control={control}
              name="total"
              type="numeric"
              inputProps={{
                label: l("Total"),
                onChange: onChangeTotal,
                isNegativeAllowed: false,
                minimumFractionDigits: 2,
                maximumFractionDigits: 6,
                isDisabled: !checkIsArray(items),
              }}
            />
            <InputAddon>
              <Box gap="s" height="100%" width="100%" align="center">
                {currencyId}
                <Flag
                  size={18}
                  locale={flagLocale}
                  borderRadius="--border-radius-circle"
                />
              </Box>
            </InputAddon>
          </InputGroup>
        </Box>
      </Box>

      <Box flex={1} maxWidth="100%" overflowX="auto">
        <Box minWidth={768} width="100%" flexDirection="column">
          <SimpleTable<AmazonOrderItemAmountCost>
            hasOuterBorder={false}
            data={items || []}
            columns={columns}
          />
        </Box>
      </Box>

      {!checkIsArray(items) ? (
        <Box flex={1} width="100%" align="center" justify="center">
          <Empty
            description={l("No data")}
            image={Empty.PRESENTED_IMAGE_SIMPLE}
          />
        </Box>
      ) : null}

      <Box
        gap="m"
        justify="end"
        hasBorder={{ top: true }}
        mSM={{
          padding: "m",
        }}
        mXL={{
          padding: "m l",
        }}
      >
        <Box mSM={{ flex: 1 }} mXL={{ flex: 0 }}>
          <Button
            fullWidth
            variant="secondary"
            disabled={isSubmitting}
            onClick={onClose}
          >
            {l("Close")}
          </Button>
        </Box>

        {checkIsArray(items) ? (
          <Box mSM={{ flex: 1 }} mXL={{ flex: 0 }}>
            <Button
              fullWidth
              disabled={isSaveButtonDisabled}
              onClick={onSubmit}
            >
              {l("Save")}
            </Button>
          </Box>
        ) : null}
      </Box>
    </Box>
  )
}
