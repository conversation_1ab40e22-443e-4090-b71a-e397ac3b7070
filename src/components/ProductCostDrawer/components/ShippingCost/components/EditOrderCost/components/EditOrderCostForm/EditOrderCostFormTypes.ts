import type { AmazonOrderItemAmountCost } from "types/Models"

type OrderCostWithId = AmazonOrderItemAmountCost & {
  id: number
}

export type EditOrderCostFormValues = {
  items: Array<OrderCostWithId>
  total: number
}

export type EditOrderCostFormProps = {
  orderAmountCosts: {
    currency_id: string
    items: Array<AmazonOrderItemAmountCost>
  }
}

export type ServerErrors = {
  status: string
  errors: Array<{
    index: number
    errors: Record<string, Array<string>>
  }>
}
