import React from "react"
import {
  Box,
  EmptyImage,
  SimpleTable,
  Skeleton,
  Typography,
} from "@develop/fe-library"

import l from "utils/intl"
import { EditOrderCostLoadingRow } from "./EditOrderCostLoadingTypes"
import { EDIT_ORDER_COST_LOADING_ROWS } from "./constants"

export const EditOrderCostLoading = ({ imageUrl }) => {
  return (
    <Box flex={1} flexDirection="column">
      <Box
        maxHeight={52}
        padding="m l"
        hasBorder={{ bottom: true }}
        align="center"
        justify="space-between"
      >
        <Typography variant="--font-body-text-7">
          {l("Order shipping cost")}
        </Typography>

        <Box width="fit-content" minWidth={80} align="center" justify="center">
          <Skeleton height="32px" width="70px" />
        </Box>
      </Box>

      <SimpleTable<EditOrderCostLoadingRow>
        hasOuterBorder={false}
        data={EDIT_ORDER_COST_LOADING_ROWS}
        columns={[
          {
            title: l("Order item shipping cost"),
            key: "amount",
            dataIndex: "id",
            width: 120,
            renderCell: () => <Skeleton height="20px" width="100px" />,
          },
          {
            title: l("Image"),
            key: "image",
            dataIndex: "id",
            width: 48,
            hasPadding: false,
            renderCell: () => (
              <Box minWidth={48} minHeight={48}>
                <EmptyImage width={48} height={48} url={imageUrl} />
              </Box>
            ),
          },
          {
            title: l("Title"),
            key: "product_title",
            dataIndex: "id",
            width: 200,
            renderCell: () => <Skeleton height="20px" width="180px" />,
          },
          {
            title: "ASIN",
            key: "product_asin",
            dataIndex: "id",
            width: 90,
            renderCell: () => <Skeleton height="20px" width="70px" />,
          },
          {
            title: "SKU",
            key: "seller_sku",
            dataIndex: "id",
            width: 90,
            renderCell: () => <Skeleton height="20px" width="70px" />,
          },
          {
            title: l("Marketplace"),
            key: "marketplace_id",
            dataIndex: "id",
            width: 90,
            renderCell: () => <Skeleton height="20px" width="70px" />,
          },
        ]}
      />
    </Box>
  )
}
