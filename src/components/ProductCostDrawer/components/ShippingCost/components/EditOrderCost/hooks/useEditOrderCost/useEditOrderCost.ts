import { useEffect, useState } from "react"
import { useDispatch, useSelector } from "react-redux"

import { ordersActions } from "actions/ordersActions"

import { editOrderCostStatesSelector } from "selectors/productCostSelectors"

const { getAmazonOrderAmountCosts } = ordersActions

export const useEditOrderCost = () => {
  const dispatch = useDispatch()

  const { order_id, imageUrl, orderAmountCosts } = useSelector(
    editOrderCostStatesSelector,
  )

  const [isLoaded, setIsLoaded] = useState(false)

  useEffect(() => {
    if (!order_id) {
      return
    }

    const successCallback = (): void => {
      setIsLoaded(true)
    }

    dispatch(
      getAmazonOrderAmountCosts({
        params: { amazonOrderId: order_id },
        successCallback,
      }),
    )
  }, [dispatch, order_id])

  return {
    isLoaded,
    orderAmountCosts,
    imageUrl,
  }
}
