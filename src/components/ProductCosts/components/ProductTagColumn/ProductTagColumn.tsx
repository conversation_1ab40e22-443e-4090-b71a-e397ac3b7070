import React, { useState } from "react"
import { useDispatch, useSelector } from "react-redux"
import { useHistory } from "react-router-dom"
import { Box, Dropdown, Icon, Popover, Typography } from "@develop/fe-library"
import { ROUTES } from "@develop/fe-library/dist/routes"
import {
  getUrlSearchParams,
  getUrlSearchParamsString,
} from "@develop/fe-library/dist/utils"

import gridActions from "actions/gridActions"

import { customerIdSelector } from "selectors/mainStateSelectors"

import { useUrlParams } from "hooks"

import { checkIsArray } from "utils/arrayHelpers"
import l from "utils/intl"

import { DropdownProductTagBody } from "../DropdownProductTagBody"

import {
  ProductTagColumnProps,
  UpdateProductHandlerReturn,
} from "./ProductTagColumnTypes"

import styles from "./productTagColumn.module.scss"
// @ts-expect-error
const { onUpdateProduct, getProducts } = gridActions

export const ProductTagColumn = ({
  productTags,
  id,
}: ProductTagColumnProps) => {
  const [errorMessage, setErrorMessage] = useState("")

  const customerId = useSelector(customerIdSelector)

  const dispatch = useDispatch()
  const history = useHistory()

  const { urlParams } = useUrlParams()

  const updateProductHandler: UpdateProductHandlerReturn = ({
    productTagsIds,
    successCallback,
  }) => {
    dispatch(
      onUpdateProduct(
        {
          productId: id,
          values: {
            tag_id: checkIsArray(productTagsIds) ? productTagsIds : [],
          },
          customerId,
        },
        () => {
          const newSearch = history.location.search.replace("n/a", "na")

          successCallback?.()
          dispatch(
            getProducts(
              getUrlSearchParams({
                locationSearch: newSearch,
              }),
            ),
          )
        },
        (error) => {
          setErrorMessage(error?.tag_id)
        },
      ),
    )
  }

  const disableTagBlockClickHandler: React.MouseEventHandler<
    HTMLBaseElement
  > = (event) => {
    event.stopPropagation()
  }
  const addTagIdToSortHandler = (id: number) => {
    return () => {
      // TODO: Refactor to object, remove baseUrl
      history.push(
        `${ROUTES.BAS_ROUTES.PATH_BAS_PRODUCT_COSTS}${getUrlSearchParamsString({
          params: {
            ...urlParams,
            tag_id: id,
          },
        })}`,
      )
    }
  }

  return (
    <Dropdown
      isVisibilityPassedToMenu
      placement="bottomRight"
      menu={
        <DropdownProductTagBody
          errorMessage={errorMessage}
          productTags={productTags}
          onSetErrorMessage={setErrorMessage}
          onUpdateProduct={updateProductHandler}
        />
      }
    >
      <div>
        {Array.isArray(productTags) && !productTags.length ? (
          <Box align="center" width="100%">
            <Typography
              className={styles.clickableText}
              color="--color-text-link"
              textAlign="left"
              variant="--font-body-text-9"
            >
              {l("No tag")}
            </Typography>
          </Box>
        ) : null}
        {checkIsArray(productTags) && productTags.length < 10 ? (
          <Box align="center" gap="m" justify="space-between" width="100%">
            <Box
              align="center"
              flexWrap="wrap"
              gap="s"
              onClick={disableTagBlockClickHandler}
            >
              {productTags.map((tag) => (
                <Popover
                  key={tag.id}
                  content={
                    <Typography
                      className={styles.link}
                      color="--color-text-link"
                      variant="--font-body-text-9"
                      wordBreak="break-all"
                      onClick={addTagIdToSortHandler(tag.id)}
                    >
                      {tag.title}
                    </Typography>
                  }
                >
                  <Box
                    backgroundColor={tag.color}
                    borderRadius="--border-radius-circle"
                    height={10}
                    width={10}
                  />
                </Popover>
              ))}
            </Box>
            <Icon isHovered name="icnEllipsis" size="--icon-size-1" />
          </Box>
        ) : null}
        {checkIsArray(productTags) && productTags.length >= 10 ? (
          <Box align="center" justify="space-between" width="100%">
            <Typography
              className={styles.clickableText}
              color="--color-text-link"
              textAlign="left"
              variant="--font-body-text-9"
            >
              {l("{count} tags", { count: productTags.length })}
            </Typography>
            <Icon
              isHovered
              color="--color-icon-active"
              name="icnChevronDown"
              size="--icon-size-1"
            />
          </Box>
        ) : null}
      </div>
    </Dropdown>
  )
}
