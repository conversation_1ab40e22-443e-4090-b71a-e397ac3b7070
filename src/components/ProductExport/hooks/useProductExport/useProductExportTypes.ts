import { ObjectType, ProductExportItemType } from "types"

export type UseProductExportHandlersType = {
  getDataHandler: (props: { searchOptions: ObjectType }) => void
  getAdditionalDataHandler: () => void
  handleCreateExportModalHide: () => void
  handleItemDownload: (props: ProductExportItemType) => void
  handleItemDelete: (props: ProductExportItemType) => void
  handleCreateExportModalShow: () => void
  handleDownloadTemplateFile: () => void
}

type PickedProps = Pick<
  UseProductExportHandlersType,
  "getDataHandler" | "getAdditionalDataHandler" | "handleCreateExportModalHide"
>

export type UseProductExportReturn = PickedProps & {
  dataSource: ProductExportItemType[]
  searchOptions: ObjectType
  totalCount: number
  isCreateModalVisible: boolean
  selectFiltersOptions: any // TODO: needs to add types globally by refactoring
  tableGridIcons: any[] // TODO: needs to add types globally by refactoring
  tableGridButtons: any[] // TODO: needs to add types globally by refactoring
  isProductExportDataLoading: boolean
}
