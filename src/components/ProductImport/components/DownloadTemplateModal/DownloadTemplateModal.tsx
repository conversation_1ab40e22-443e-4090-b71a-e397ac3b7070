import React, { useState } from "react"
import { Select } from "@develop/fe-library"

import { RestrictedModal } from "components/shared/RestrictedModal"

import l from "utils/intl"
import { translateOptions } from "utils/translateOptions"

import { OPTIONS_TEMPLATE_HANDLER_NAMES } from "constants/productImport"

import { TemplateHandlerValues } from "types/ProductImport"

export const DownloadTemplateModal = ({
  error = "",
  managePermission,
  popoverMessage,
  title,
  onClose = () => {},
  onDownloadTemplate,
}) => {
  const [templateHandlerName, setTemplateHandlerName] = useState<
    TemplateHandlerValues | string
  >("")

  const submitHandler = () => {
    if (!templateHandlerName) {
      return
    }

    onDownloadTemplate(templateHandlerName)
  }

  return (
    <RestrictedModal
      visible
      managePermission={managePermission}
      okButtonProps={{ disabled: !templateHandlerName }}
      okText={l("Download")}
      popoverMessage={popoverMessage}
      title={l(title)}
      onCancel={onClose}
      onOk={submitHandler}
    >
      <Select
        isFullWidth
        isRequired
        errorMessage={error}
        label={l("Select a template")}
        options={translateOptions(OPTIONS_TEMPLATE_HANDLER_NAMES)}
        onChangeValue={setTemplateHandlerName}
      />
    </RestrictedModal>
  )
}
