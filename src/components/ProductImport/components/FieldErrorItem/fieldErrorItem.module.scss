@import "assets/styles/variables.scss";

.field {
  display: flex;
  flex-direction: column;
  gap: 5px;

  @media screen and (min-width: $xs) {
    gap: 0;
    flex-direction: row;
  }
}

.fieldTitle.fieldTitle.fieldTitle {
  margin-right: 10px;
  min-width: 100px;
  width: 100px;
}

.fieldValue {
  color: $text_second;
}

.item {
  display: flex;
  gap: 10px;
  flex-direction: column;
  background-color: $main_bg;
  border-radius: 2px;
  padding-bottom: 10px;

  &:last-child {
    padding-bottom: 0;
  }
}
