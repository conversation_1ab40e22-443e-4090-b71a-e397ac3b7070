import React from "react"
import cn from "classnames"
import { Popover, Flag, Box, Typography, EmptyImage } from "@develop/fe-library"
// @ts-ignore
import HighlightWords from "react-highlight-words"
// @ts-ignore
import TextEllipsis from "react-text-ellipsis/src"

import { countryCode } from "utils/countryCode"

import GridProduct from "models/GridProduct"

import { ProductInfo } from "interfaces/GridInterfaces"
import { SelectItemProps, HandleSkuSelectProps } from "./SelectItemTypes"

import styles from "./selectItem.module.scss"

export const SelectItem = ({
  option,
  searchValue,
  onSkuSelect,
}: SelectItemProps) => {
  const handleClearMouseUp = (event: React.MouseEvent<HTMLElement>): void => {
    event.stopPropagation()
  }

  const handleSkuSelect: HandleSkuSelectProps =
    (option) =>
    (event): void => {
      event.stopPropagation()
      event.preventDefault()

      onSkuSelect(option)
    }

  const product = new GridProduct({
    seller_id: option.seller_id || "",
    asin: option.asin || "",
  } as ProductInfo)
  const imageUrl = product.getImageUrl()

  return (
    <Box key={option.id} padding="m" gap="m">
      <Box justify="center" width={40} flexShrink={0}>
        <EmptyImage url={imageUrl} width={40} height={40} />
      </Box>
      <Box gap="s" flexDirection="column">
        <TextEllipsis
          tagClass={styles.productTitle}
          ellipsisChars="..."
          lines={1}
          tooltip={option.title}
        >
          <HighlightWords
            className={styles.highlight}
            autoEscape
            searchWords={[searchValue]}
            textToHighlight={option.title}
          />
        </TextEllipsis>

        <Box gap="l">
          <Popover content={option?.marketplaceTitle} placement="bottom">
            <Flag
              onClick={handleClearMouseUp}
              locale={countryCode(option?.country)}
              size={16}
              borderRadius="--border-radius-circle"
            />
          </Popover>

          <Box gap="s">
            <Typography
              variant="--font-body-text-9"
              color="--color-text-second"
            >
              ASIN
            </Typography>
            <Typography variant="--font-body-text-9" component="span">
              <a
                target="_blank"
                rel="noopener noreferrer"
                href={option.amazonProductLink}
                onClick={handleClearMouseUp}
              >
                <HighlightWords
                  className={styles.highlight}
                  autoEscape
                  searchWords={[searchValue]}
                  textToHighlight={option.asin}
                />
              </a>
            </Typography>
          </Box>

          <Box gap="s" onClick={handleSkuSelect(option)}>
            <Typography
              variant="--font-body-text-9"
              color="--color-text-second"
            >
              SKU
            </Typography>
            <Typography
              variant="--font-body-text-9"
              color="--color-text-second"
            >
              <TextEllipsis ellipsisChars="..." lines={1} tooltip={option.sku}>
                <HighlightWords
                  className={cn(styles.highlight, styles.productSku)}
                  autoEscape
                  searchWords={[searchValue]}
                  textToHighlight={option.sku}
                />
              </TextEllipsis>
            </Typography>
          </Box>
        </Box>
      </Box>
    </Box>
  )
}
