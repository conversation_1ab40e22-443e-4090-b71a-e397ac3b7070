import React from "react"
import { FormItems, Modal } from "@develop/fe-library"

import l from "utils/intl"

import { useCreateNewTagModal } from "./hooks"

import { CreateNewTagModalProps } from "./CreateNewTagModalTypes"

export const CreateNewTagModal = ({ onSuccess }: CreateNewTagModalProps) => {
  const {
    toggleModalHandler,
    handleSubmit,
    submitFormHandler,
    form,
    isCreateProductTagModalVisible,
  } = useCreateNewTagModal({ onSuccess })

  if (!isCreateProductTagModalVisible) {
    return null
  }

  const items = [
    {
      name: "title",
      type: "text",
      inputProps: {
        label: l("Tag name"),
        isRequired: true,
      },
      gridItemProps: { always: 12 },
    },
  ]

  return (
    <Modal
      visible
      cancelButtonText={l("Cancel")}
      okButtonText={l("Save")}
      title={l("Create tag")}
      width="--modal-size-xs"
      onCancel={toggleModalHandler(false)}
      onClose={toggleModalHandler(false)}
      onOk={handleSubmit(submitFormHandler)}
    >
      <FormItems
        form={form}
        items={items}
        gridContainerProps={{
          gapMSM: "m",
          justify: "end",
        }}
      />
    </Modal>
  )
}
