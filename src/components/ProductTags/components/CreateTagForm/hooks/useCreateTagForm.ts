import { useForm } from "react-hook-form"
import { useDispatch, useSelector } from "react-redux"

import productTagsActions from "actions/productTagsActions"

import { productTagColorsSelector } from "selectors/productTagColorsSelectors"

import { ErrorTypes } from "components/ProductTags/ProductTagsTypes"

import { useUrlParams } from "hooks"

import { checkIsArray } from "utils/arrayHelpers"
import { generateRandomColor } from "utils/colors"

import { Color, Tag, TagColor } from "types"

import { FormFieldsKeysTypes } from "../CreateTagFormTypes"

const { getProductTags, createProductTag } = productTagsActions

export const useCreateTagForm = () => {
  const productTagColors: TagColor[] = useSelector(productTagColorsSelector)

  const form = useForm<Tag>({
    defaultValues: {
      title: "",
    },
  })

  const { urlParams } = useUrlParams()

  const dispatch = useDispatch()

  const {
    handleSubmit,
    setValue,
    setError,
    reset,
    formState: { isSubmitting },
    watch,
  } = form

  const watchColorField = watch("color")

  const handleErrors = (errors: ErrorTypes): void => {
    if (!errors) {
      return
    }

    if (checkIsArray(errors)) {
      errors.forEach(({ field, message }) => {
        setError(field as FormFieldsKeysTypes, { type: "custom", message })
      })
    }
  }

  const handleFormSubmit = (data: Tag): void => {
    const { color: selectedColor, ...restSubmitData } = data
    const randomColor = generateRandomColor(productTagColors)

    dispatch(
      createProductTag({
        payload: { ...restSubmitData, color: selectedColor || randomColor },
        successCallback: () => {
          reset()
          dispatch(getProductTags(urlParams))
        },
        failureCallback: handleErrors,
      }),
    )
  }

  const setValueHandler =
    (name: FormFieldsKeysTypes) =>
    (value: Color["hex"]): void => {
      if (value) {
        setValue(name, value)
      }
    }

  return {
    setValueHandler,
    handleSubmit,
    handleFormSubmit,
    isSubmitting,
    form,
    watchColorField,
  }
}
