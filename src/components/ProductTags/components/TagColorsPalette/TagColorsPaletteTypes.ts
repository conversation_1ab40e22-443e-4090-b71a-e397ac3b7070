import { Color, TagColor } from "types"

export type TagColorsPaletteProps = {
  onChange: (tagColor: TagColor["tag_color"]) => void
  isColorDeletable?: boolean
  initialColor?: TagColor["tag_color"] | null
}

export type UseTagColorsPaletteProps = {
  initialColor?: TagColorsPaletteProps["initialColor"]
  onChange: TagColorsPaletteProps["onChange"]
}

export type UseTagColorsPaletteReturn = {
  productTagColors: TagColor[]
  selectedTagColor: TagColorsPaletteProps["initialColor"]
  previewColor: Color["hex"]
  handleCheckboxChange: (tagColor: TagColor) => (isChecked: boolean) => void
  handleDeleteColor: (tagColor: TagColor) => () => void
  handleColorPickerModalVisible: (isVisible: boolean) => () => void
  handleColorAdd: () => void
  handleChangeColor: (color: Color) => void
  isOneColor: boolean
  isColorPickerModalVisible: boolean
  isColorAddDisabled: boolean
}
