import { useEffect, useState } from "react"
import { useDispatch, useSelector } from "react-redux"

import productTagColorActions from "actions/productTagColorActions"

import { productTagColorsSelector } from "selectors/productTagColorsSelectors"

import { useUrlParams } from "hooks"

import { setConfirm } from "utils/confirm"
import l from "utils/intl"

import { Color, TagColor } from "types"

import {
  TagColorsPaletteProps,
  UseTagColorsPaletteProps,
  UseTagColorsPaletteReturn,
} from "../TagColorsPaletteTypes"

const { getProductTagColors, createProductTagColor, deleteProductTagColor } =
  productTagColorActions

export const useTagColorsPalette = ({
  initialColor,
  onChange,
}: UseTagColorsPaletteProps): UseTagColorsPaletteReturn => {
  const dispatch = useDispatch()

  const [isColorAddDisabled, setIsColorAddDisabled] = useState(false)
  const [selectedTagColor, setSelectedTagColor] =
    useState<TagColorsPaletteProps["initialColor"]>(null)
  const [previewColor, setPreviewColor] = useState<Color["hex"]>("#0055cc")
  const [isColorPickerModalVisible, setIsColorPickerModalVisible] =
    useState(false)

  const productTagColors: TagColor[] = useSelector(productTagColorsSelector)

  const { urlParams } = useUrlParams()

  const isOneColor = productTagColors.length === 1

  const handleCheckboxChange =
    ({ tag_color }: TagColor) =>
    (isChecked: boolean): void => {
      const isSelectedCurrentColor =
        selectedTagColor === tag_color && !isChecked

      if (isSelectedCurrentColor) {
        onChange("")
        setSelectedTagColor(null)

        return
      }

      onChange(tag_color)
      setSelectedTagColor(tag_color)
    }

  const handleColorPickerModalVisible = (isVisible: boolean) => (): void => {
    setIsColorPickerModalVisible(isVisible)
  }

  const handleChangeColor = ({ hex }: Color): void => {
    setPreviewColor(hex)
  }

  const handleColorAdd = (): void => {
    dispatch(
      createProductTagColor({
        payload: { tag_color: previewColor, type: "CUSTOM" },
        successCallback: () => {
          dispatch(getProductTagColors(urlParams))
          setIsColorPickerModalVisible(false)
        },
        failureCallback: null,
      }),
    )
  }

  const handleColorDelete = (id: number) => (): void => {
    dispatch(
      deleteProductTagColor({
        id,
        successCallback: () => {
          dispatch(getProductTagColors(urlParams))
        },
      }),
    )
  }

  const handleDeleteColor =
    ({ id }: TagColor) =>
    (): void => {
      if (isOneColor) {
        return
      }

      setConfirm({
        title: l("Delete color"),
        message: l("Are you sure you want to delete this color?"),
        onOk: handleColorDelete(id),
        okText: l("Delete"),
      })
    }

  useEffect(() => {
    const hasSameColor = productTagColors?.some(
      ({ tag_color }) => tag_color === previewColor,
    )

    setIsColorAddDisabled(hasSameColor)
  }, [previewColor, productTagColors])

  useEffect(() => {
    setSelectedTagColor(initialColor)
  }, [initialColor])

  return {
    productTagColors,
    selectedTagColor,
    previewColor,
    handleCheckboxChange,
    handleDeleteColor,
    handleColorPickerModalVisible,
    handleColorAdd,
    handleChangeColor,
    isOneColor,
    isColorPickerModalVisible,
    isColorAddDisabled,
  }
}
