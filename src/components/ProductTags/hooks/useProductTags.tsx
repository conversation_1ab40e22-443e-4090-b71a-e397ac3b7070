import { useEffect } from "react"
import { useDispatch, useSelector } from "react-redux"

import productTagColorActions from "actions/productTagColorActions"
import productTagsActions from "actions/productTagsActions"

import { isLoadingTagColorsSelector } from "selectors/productTagColorsSelectors"
import {
  productTagsSelector,
  productTagsStatusSelector,
} from "selectors/productTagsSelectors"

import getMainApp from "components/MainAppProvider"

import { ASYNC_STATUSES } from "constants/async"

import { AsyncStatus, Tag } from "types"

const {
  getProductTags,
  displayDeleteProductTagModal,
  displayUpdateEditProductTagModal,
} = productTagsActions

const { getProductTagColors } = productTagColorActions

export const useProductTags = () => {
  const dispatch = useDispatch()

  const { history: mainAppHistory } = getMainApp()

  useEffect(() => {
    dispatch(getProductTagColors({}))
    dispatch(getProductTags({}))
  }, [])

  const productTags: Tag[] = useSelector(productTagsSelector)
  const tagsStatus: AsyncStatus = useSelector(productTagsStatusSelector)
  const isTagColorsLoading = useSelector(isLoadingTagColorsSelector)

  const deleteTagHandler = (tag: Tag) => (): void => {
    dispatch(displayDeleteProductTagModal(true, tag))
  }

  const editTagHandler = (tag: Tag) => (): void => {
    dispatch(displayUpdateEditProductTagModal(true, tag))
  }

  const handleGoBack = (): void => {
    mainAppHistory?.goBack()
  }

  const hasHistory = mainAppHistory?.length !== 1
  const isDataLoading: boolean =
    tagsStatus === ASYNC_STATUSES.PENDING && isTagColorsLoading

  return {
    productTags,
    isDataLoading,
    hasHistory,
    handleGoBack,
    editTagHandler,
    deleteTagHandler,
  }
}
