import { useEffect, useState } from "react"
import { useDispatch, useSelector } from "react-redux"

import exportTemplatesActions from "actions/exportTemplatesActions"
import recurrentExportSettingsActions from "actions/recurrentExportSettingsActions"

import { allExportTemplatesSelector } from "selectors/exportTemplatesSelectors"
import {
  modalVisibleSelector,
  recurrentExportSettingsSelector,
} from "selectors/recurrentExportSettingsSelectors"

import { checkIsArray } from "utils/arrayHelpers"
import { FORM_IS_CHANGED, setConfirm } from "utils/confirm"

const {
  getRecurrentExportSettings,
  addRecurrentExportSetting,
  updateRecurrentExportSetting,
  changeStatusForRecurrentExportSetting,
  deleteRecurrentExportSetting,
  displayRecurrentExportSettingModal,
  showRecurrentExportSettingConfirmationModal,
} = recurrentExportSettingsActions

const { getAllExportTemplates } = exportTemplatesActions

export const useRecurrentExportSettings = () => {
  const dispatch = useDispatch()

  const recurrentExportSettings = useSelector(recurrentExportSettingsSelector)
  const modalVisible = useSelector(modalVisibleSelector)
  const allExportTemplates = useSelector(allExportTemplatesSelector)
  // Local states
  const [activeItem, setActiveItem] = useState(undefined)
  const [isFormChanged, setIsFormChanged] = useState(false)

  useEffect(() => {
    dispatch(getAllExportTemplates(null))
    dispatch(getRecurrentExportSettings(null))
  }, [dispatch])

  const triggerModal = () => {
    dispatch(displayRecurrentExportSettingModal(!modalVisible))
  }

  const closeModal = () => {
    setActiveItem(undefined)
    dispatch(displayRecurrentExportSettingModal(false))
  }

  const handleFormChange = (touched) => {
    setIsFormChanged(touched)
  }

  const handleSetActiveItem = (id) => {
    const item = recurrentExportSettings.find((item) => item.id === id)

    setActiveItem(item)
  }

  const handleDelete = (id) => {
    dispatch(deleteRecurrentExportSetting(id))
  }

  const handleStatusChange = (id) => {
    const item = recurrentExportSettings.find((item) => item.id === id)

    dispatch(changeStatusForRecurrentExportSetting(item, null, null))
  }

  const handleSave = (values, failureCallback) => {
    const { handler_name } = allExportTemplates.find(
      (item) => item.id === values.template_id,
    )

    const payload = {
      ...values,
      handler_name,
      auth_login: values.should_use_auth ? values.auth_login : undefined,
      auth_password: values.should_use_auth ? values.auth_password : undefined,
    }

    if (values.id) {
      dispatch(
        updateRecurrentExportSetting(payload, closeModal, failureCallback),
      )
    } else {
      dispatch(addRecurrentExportSetting(payload, closeModal, failureCallback))
    }
  }

  const handleCancel = () => {
    if (!isFormChanged) {
      closeModal()
    } else {
      setConfirm({
        template: FORM_IS_CHANGED,
        onOk: () => {
          dispatch(showRecurrentExportSettingConfirmationModal(false))
          closeModal()
          setIsFormChanged(false)
        },
        onCancel: () => {
          dispatch(showRecurrentExportSettingConfirmationModal(false))
        },
      })
      dispatch(showRecurrentExportSettingConfirmationModal(true))
    }
  }

  return {
    recurrentExportSettings,
    modalVisible,
    showConnectLostAndFoundBlock: false,
    allExportTemplates,
    hasExportTemplates: checkIsArray(allExportTemplates),
    activeItem,
    triggerModal,
    handleFormChange,
    handleSetActiveItem,
    handleDelete,
    handleStatusChange,
    handleSave,
    handleCancel,
  }
}
