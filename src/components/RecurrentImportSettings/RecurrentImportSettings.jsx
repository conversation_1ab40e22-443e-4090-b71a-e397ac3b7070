import React from "react"
import PropTypes from "prop-types"

import { RecurrentSettings } from "components/RecurrentSettings"

import { importExportSettingKeys } from "constants/recurrentSettingsConstants"

import { useRecurrentImportSettings } from "./hooks"

export const RecurrentImportSettings = ({
  title,
  permissionCode,
  viewPermissionCode,
}) => {
  const {
    recurrentImportSettings,
    modalVisible,
    activeItem,
    triggerModal,
    errors,
    handleFormChange,
    handleSetActiveItem,
    handleDelete,
    handleStatusChange,
    handleSave,
    handleCancel,
  } = useRecurrentImportSettings()

  return (
    <RecurrentSettings
      activeItem={activeItem}
      errors={errors}
      modalVisible={modalVisible}
      permissionCode={permissionCode}
      recurrentSettings={recurrentImportSettings}
      title={title}
      triggerModal={triggerModal}
      type={importExportSettingKeys.types.IMPORT}
      viewPermissionCode={viewPermissionCode}
      onCancel={handleCancel}
      onDelete={handleDelete}
      onFormChange={handleFormChange}
      onSave={handleSave}
      onSetActiveItem={handleSetActiveItem}
      onStatusChange={handleStatusChange}
    />
  )
}

RecurrentImportSettings.propTypes = {
  title: PropTypes.string.isRequired,
  permissionCode: PropTypes.string.isRequired,
  viewPermissionCode: PropTypes.string.isRequired,
}
