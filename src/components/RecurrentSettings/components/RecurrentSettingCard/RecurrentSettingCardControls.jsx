import React from "react"
import PropTypes from "prop-types"
import { Typography, Box, IconPopover } from "@develop/fe-library"

import { RestrictedSwitch } from "components/Restrict"
import { RestrictedIconPopover } from "components/shared/RestrictedIconPopover"

import l from "utils/intl"
import { setConfirm, DELETE_ITEM } from "utils/confirm"

import { restrictPopoverMessages } from "constants/permissions"

import styles from "./recurrentSettingCard.module.scss"

export const RecurrentSettingCardControls = ({
  onDelete,
  recurrentSettingId,
  triggerModal,
  onSetActiveItem,
  onStatusChange,
  isEnabled,
  permissionCode,
  viewPermissionCode,
  isUrlBroken,
}) => {
  const isEnabledText = l(isEnabled ? "Enabled" : "Disabled")

  const handleChangeStatus = () => {
    onStatusChange(recurrentSettingId)
  }

  const handleDelete = () => {
    setConfirm({
      template: DELETE_ITEM,
      onOk: () => onDelete(recurrentSettingId),
    })
  }

  const handleEdit = () => {
    onSetActiveItem(recurrentSettingId)
    triggerModal()
  }

  return (
    <Box align="center" gap="m" justify="space-between">
      <Box align="center">
        <RestrictedSwitch
          managePermission={permissionCode}
          popoverMessage={restrictPopoverMessages.alter}
          checked={isEnabled}
          onChange={handleChangeStatus}
          className={styles.toggle}
          popoverPlacement="topLeft"
        />
        <Typography variant="--font-body-text-7">{isEnabledText}</Typography>
      </Box>
      <Box gap="m" className={styles.iconContainer}>
        {isUrlBroken ? (
          <IconPopover
            content={l(
              "Import file is not available. Please check if the import file exists by provided URL.",
            )}
            name="icnWarning"
            size="--icon-size-5"
            placement="top"
            contentClassName={styles.brokenUrlPopover}
          />
        ) : null}
        <RestrictedIconPopover
          managePermission={permissionCode}
          popoverMessage={restrictPopoverMessages.alter}
          content={l("Delete")}
          onClick={handleDelete}
          name="icnDeleteOutlined"
          size="--icon-size-5"
        />
        <RestrictedIconPopover
          managePermission={viewPermissionCode}
          popoverMessage={restrictPopoverMessages.view}
          content={l("Settings")}
          onClick={handleEdit}
          name="icnSetting"
          size="--icon-size-5"
        />
      </Box>
    </Box>
  )
}

RecurrentSettingCardControls.propTypes = {
  onDelete: PropTypes.func.isRequired,
  recurrentSettingId: PropTypes.oneOfType([PropTypes.string, PropTypes.number])
    .isRequired,
  triggerModal: PropTypes.func.isRequired,
  onSetActiveItem: PropTypes.func.isRequired,
  onStatusChange: PropTypes.func.isRequired,
  isEnabled: PropTypes.bool,
  permissionCode: PropTypes.string.isRequired,
  viewPermissionCode: PropTypes.string.isRequired,
  isUrlBroken: PropTypes.bool,
}

RecurrentSettingCardControls.defaultProps = {
  isEnabled: false,
  isUrlBroken: false,
}
