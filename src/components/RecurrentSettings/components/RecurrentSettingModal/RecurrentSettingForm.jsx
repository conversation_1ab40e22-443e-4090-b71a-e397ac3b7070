import React from "react"
import PropTypes from "prop-types"
import { Form } from "formik"
import { capitalize } from "lodash"

import { FooterModal } from "components/shared/FooterModal"
import { CronInput } from "components/CronInput"
import { RecurrentExportSettingFormFields } from "./RecurrentExportSettingFormFields"
import { RecurrentImportSettingFormFields } from "./RecurrentImportSettingFormFields"
import { importExportSettingKeys } from "constants/recurrentSettingsConstants"
import { restrictPopoverMessages } from "constants/permissions"

import l from "utils/intl"

export const RecurrentSettingForm = ({
  values,
  isSubmitting,
  handleSubmit,
  onCancel,
  canSubmit,
  setFieldValue,
  templates,
  initialValues,
  type,
  submitForm,
  errors,
  hasExportTemplates,
  permissionCode,
}) => {
  const handleCronChange = (cron) => {
    setFieldValue("cron_expr", cron)
  }

  return (
    <Form onSubmit={handleSubmit} noValidate>
      {type === importExportSettingKeys.types.EXPORT ? (
        <RecurrentExportSettingFormFields
          templates={templates}
          hasExportTemplates={hasExportTemplates}
        />
      ) : null}

      {type === importExportSettingKeys.types.IMPORT ? (
        <RecurrentImportSettingFormFields hasAuth={values.should_use_auth} />
      ) : null}

      <CronInput
        title={l(`${capitalize(type)} will run`)}
        onChange={handleCronChange}
        initialValue={initialValues.cron_expr}
        errorMessage={errors?.cron_expr}
      />

      <FooterModal
        isNegativeMargin
        okButtonText={l("Save")}
        okButtonProps={{
          managePermission: permissionCode,
          popoverMessage: restrictPopoverMessages.alter,
          disabled: isSubmitting || !canSubmit,
        }}
        onOk={submitForm}
        onCancel={onCancel}
        cancelButtonProps={{
          disabled: isSubmitting,
          name: "Close",
        }}
      />
    </Form>
  )
}

RecurrentSettingForm.propTypes = {
  values: PropTypes.object.isRequired,
  isSubmitting: PropTypes.bool,
  handleSubmit: PropTypes.func.isRequired,
  onCancel: PropTypes.func.isRequired,
  canSubmit: PropTypes.bool,
  setFieldValue: PropTypes.func.isRequired,
  templates: PropTypes.array.isRequired,
  initialValues: PropTypes.object.isRequired,
  setValues: PropTypes.func.isRequired,
  type: PropTypes.oneOf([
    importExportSettingKeys.types.IMPORT,
    importExportSettingKeys.types.EXPORT,
  ]).isRequired,
  submitForm: PropTypes.func.isRequired,
  hasExportTemplates: PropTypes.bool,
  permissionCode: PropTypes.string.isRequired,
}

RecurrentSettingForm.defaultProps = {
  isSubmitting: false,
  onCancel: () => {},
  canSubmit: false,
  hasExportTemplates: false,
}
