@import "assets/styles/variables.scss";

.modalWrapper {
  :global(.ant-modal-body) {
    padding: var(--padding-m);
  }

  @media (min-width: $sm) {
    :global(.ant-modal-body) {
      padding: var(--margin-l);
    }
  }
}

.protectedField {
  margin-top: var(--margin-m);
  align-items: center;
}

.fieldsWrapper {
  padding-bottom: var(--padding-m);

  @media (min-width: $sm) {
    position: relative;
    padding-bottom: var(--padding-l);
  }

  &::after {
    @media (min-width: $sm) {
      position: absolute;
      bottom: 0;
      left: 0;
      content: "";
      width: calc(100% + var(--margin-xl));
      transform: translateX(-20px);
      height: 1px;
      background-color: $border_main;
    }
  }
}

.field {
  width: 100%;
}

.selectError {
  :global(.with-error-field-icon) {
    right: 30px;
  }
}

.selectField {
  width: 100%;
}

.settingsInfo {
  color: $text_second;
  line-height: 1.38;

  @media (max-width: $sm) {
    margin-bottom: var(--margin-m);
  }
}

.numbers {
  margin-bottom: calc(-1 * var(--margin-m));

  :global(.ant-checkbox-wrapper) {
    margin-left: 0;
    margin-bottom: var(--margin-m);

    :global(.ant-checkbox + span) {
      margin-left: 0;
      margin-right: var(--margin-m);

      @media (max-width: $xs) {
        width: 47px;
      }
    }
  }
}

.letters {
  :global(.ant-checkbox-wrapper) {
    :global(.ant-checkbox + span) {
      width: 47px;
    }
  }
}

.days {
  :global(.ant-checkbox-wrapper) {
    :global(.ant-checkbox + span) {
      @media (max-width: $xs) {
        width: 58px;
      }
    }
  }
}

.active {
  background-color: var(--color-row-select);

  .text {
    color: $text_main;
  }
}

.isActive {
  padding-bottom: 20px;
}

.advancedOptionsSwitch {
  margin-right: var(--margin-m);
}

.fieldOptions {
  flex: 1 1 0;
  width: 100%;
}

.optionsField {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}

.advancedItemContainer {
  display: flex;
  align-items: center;
  padding: 20px;
  gap: 10px;
}

.radioOption {
  margin-right: 0;
}

.text {
  color: $text_second;
  white-space: nowrap;
  display: flex;
  align-items: center;
  width: 100%;
  gap: 10px;

  @media (max-width: $xs) {
    gap: initial;
    flex-direction: column;
    align-items: flex-start;
  }
}

.withSpace {
  @media (min-width: $sm) {
    margin-right: 20px;
  }
}

.capitalize {
  &::first-letter {
    text-transform: uppercase;
  }
}
