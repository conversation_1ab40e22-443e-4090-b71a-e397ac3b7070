import React from "react"
import { Popover } from "@develop/fe-library"

import { withRestrict } from "components/hocs/withRestrict"

import { RestrictedContainerProps } from "./RestrictTypes"

const RestrictedContainer = ({
  children,
  isDisabled,
  content,
  placement,
}: RestrictedContainerProps): React.ReactElement => {
  const processedChildren =
    typeof children === "function"
      ? children(!!isDisabled)
      : React.Children.map(children, (child) => {
          if (React.isValidElement(child)) {
            return React.cloneElement(child, {
              isDisabled,
              disabled: isDisabled,
            })
          }

          return child
        })

  if (content) {
    return (
      <Popover content={content} placement={placement}>
        <>{processedChildren}</>
      </Popover>
    )
  }

  return processedChildren
}

const Restrict = withRestrict<RestrictedContainerProps>(
  RestrictedContainer,
  "",
  {
    isPopover: true,
  },
)

export { Restrict }
