import { Checkbox as AntdCheckbox, Select, Switch } from "antd"
import { Icon, Checkbox } from "@develop/fe-library"
import {
  withRestrict,
  restrictEventHandlerKeys,
} from "components/hocs/withRestrict"

const { Group: CheckboxGroup } = AntdCheckbox

export const RestrictedSpan = withRestrict(
  "span",
  restrictEventHandlerKeys.onClick
)

export const RestrictedCheckbox = withRestrict(
  Checkbox,
  restrictEventHandlerKeys.onChange
)
export const RestrictedCheckboxGroup = withRestrict(
  CheckboxGroup,
  restrictEventHandlerKeys.onChange
)
export const RestrictedSelect = withRestrict(
  Select,
  restrictEventHandlerKeys.onChange
)
export const RestrictedSwitch = withRestrict(
  Switch,
  restrictEventHandlerKeys.onChange
)
export const RestrictedCompatibleIcon = withRestrict(
  Icon,
  restrictEventHandlerKeys.onClick
)
