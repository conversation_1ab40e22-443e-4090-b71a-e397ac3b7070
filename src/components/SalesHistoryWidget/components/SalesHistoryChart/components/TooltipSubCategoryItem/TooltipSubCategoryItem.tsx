import React from "react"
import { Box, Typography } from "@develop/fe-library"
import { getObjectValues } from "@develop/fe-library/dist/utils"

import { useShouldBlur } from "components/SalesHistoryWidget/hooks/useShouldBlur/useShouldBlur"
import { RestrictedBlurred } from "components/shared/RestrictedBlurred"

import { useUrlParams } from "hooks"

import l from "utils/intl"
import ln from "utils/localeNumber"

import { LegendColorIndicator } from "../LegendColorIndicator"

import { DashboardFiltersMainFilterParams } from "types/UrlParams"

import { TooltipSubCategoryItemProps } from "./TooltipSubCategoryItemTypes"

export const TooltipSubCategoryItem = ({
  subcategory,
  payload,
  isUnits,
}: TooltipSubCategoryItemProps) => {
  const { urlParams } = useUrlParams<DashboardFiltersMainFilterParams>()
  const { getShouldBlur } = useShouldBlur()

  const payloadItem = getObjectValues(payload).find((childCategory) => {
    // Find category element in actual list of available categories (this list can be changed via manage view)
    // For example if element is switched off do not show it in tooltip, and subtract it's value from total sum
    return childCategory.dataKey?.split(".")?.pop() === subcategory.id
  })

  if (!payloadItem) {
    return null
  }

  const getUnitsValue = (amount: string | number): string =>
    isUnits
      ? ln(amount, 0)
      : ln(amount, 2, { currency: urlParams.currency_code })

  const shouldBlur: boolean = getShouldBlur(subcategory.id)

  return (
    <Box flexDirection="row" justify="space-between">
      <Box align="center" gap="m">
        <LegendColorIndicator
          color={payloadItem.color}
          variant={isUnits ? "unit" : "category"}
        />

        <Typography color="--color-text-main" variant="--font-body-text-9">
          {l(payloadItem.name)}
        </Typography>
      </Box>

      <Typography
        color="--color-text-second"
        variant="--font-body-text-9"
        whiteSpace="nowrap"
      >
        <RestrictedBlurred
          magnitude={10_000}
          shouldBlur={shouldBlur}
          variability={2}
        >
          {({ generatedValue, shouldBlur }) =>
            getUnitsValue(shouldBlur ? generatedValue : payloadItem.value)
          }
        </RestrictedBlurred>
      </Typography>
    </Box>
  )
}
