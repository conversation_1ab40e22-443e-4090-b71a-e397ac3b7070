import * as htmlToImage from "html-to-image"

import { DownloadFileProps } from "./downloadFileTypes"

export const downloadFile = ({
  element,
  fileName,
  onSuccessCallback = () => {},
  onErrorCallback = () => {},
}: DownloadFileProps): void => {
  htmlToImage
    .toBlob(element, { skipFonts: true })
    .then((content) => {
      const link = document.createElement("a")

      if (content) {
        link.href = window.URL.createObjectURL(content)
        link.download = fileName
        link.click()
      }
    })
    .catch(onErrorCallback)
    .finally(onSuccessCallback)
}
