import { BaseSyntheticEvent } from "react"
import { InputMode, SidebarPanelTabType } from "@develop/fe-library"
import { Interval } from "date-fns"

import { HandleRefetchFormFiltersProps } from "components/SegmentsSidebar/hooks/useSegmentsSidebar/useSegmentsSidebarTypes"

import {
  ProductFiltersGeneralParams,
  ProductFiltersUniqueIdsParams,
  SegmentUrlParams,
  TagsUrlParams,
} from "types/UrlParams"
import { DashboardFiltersMainFilterParams } from "types/UrlParams/pages/dashboard"

export type MarketplacesFormType = {
  marketplace_id?: string[]
}

export type OfferTypeFormType = {
  offer_type?: string
}

export type DateRangeFormType = {
  dateRange: {
    inputMode: InputMode
    selected: Interval
  }
}

export type ProductFieldFormType = {
  productId?: number | null
}

export type ProductIdsFormType = ProductFiltersUniqueIdsParams

export type ParentASINFormType = {
  parent_asin?: string
}

export type ProductFieldsListType = ProductFieldFormType &
  ProductFiltersUniqueIdsParams &
  ParentASINFormType

export type ProductIdsKeysType = keyof ProductIdsFormType

export type ProductFieldsListKeysType = keyof ProductFieldsListType

export type ProductFormType = ProductFieldFormType &
  ParentASINFormType & {
    brand?: string[]
    product_type?: string[]
    stock_type?: string[]
    manufacturer?: string[]
    adult_product?: boolean
  }

export type ProductFiltersExtraFormType = {
  has_asin?: boolean
  has_sku?: boolean
  has_ean?: boolean
  has_upc?: boolean
  has_isbn?: boolean
}

export type ProductFiltersExtraFormKeys = keyof ProductFiltersExtraFormType

export type ProductFiltersTagsFormType = {
  tags?: string[]
}

export type ProductFiltersTagsFormKeys = keyof ProductFiltersTagsFormType

// DESC: Product filters
export type ProductFiltersFormType = ProductFiltersExtraFormType &
  ProductIdsFormType &
  ProductFormType &
  ProductFiltersTagsFormType

export type ProductFiltersFormKeysType = keyof ProductFiltersFormType

// DESC: Main filters
export type MainFiltersFormType = Omit<
  DashboardFiltersMainFilterParams,
  "marketplace_id" | "from" | "to" | "inputMode"
> &
  OfferTypeFormType &
  DateRangeFormType &
  MarketplacesFormType

export type MainFiltersFormKeysType = keyof MainFiltersFormType

// DESC: Full form
export type DashboardFiltersFormType = Omit<
  MainFiltersFormType,
  "isFromRepricer"
> &
  ProductFiltersFormType

export type DashboardFiltersFormKeysType = keyof DashboardFiltersFormType

export type DashboardFiltersFormWithoutExtraType = Omit<
  DashboardFiltersFormType,
  ProductFiltersExtraFormKeys
>

// DESC: URL Params type
export type DashboardFiltersParams = DashboardFiltersMainFilterParams &
  ProductFiltersGeneralParams &
  SegmentUrlParams &
  TagsUrlParams

export type DashboardFiltersParamsKeysType =
  | keyof DashboardFiltersParams
  | (string & {})

export type FiltersProps = {
  activeTab: SidebarPanelTabType
  isDefaultValuesInitiated: boolean
  onFormReset: () => void
  onFormSubmit: (event?: BaseSyntheticEvent) => Promise<void>
  onRefetchFormFilters: (props: HandleRefetchFormFiltersProps) => void
  onCloseTab?: () => void
}
