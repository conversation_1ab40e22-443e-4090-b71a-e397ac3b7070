import { useMemo } from "react"
import { useFormContext, useWatch } from "react-hook-form"

import { MarketplacesOption } from "components/SegmentsSidebar/components/Filters/components/MainFilter/components"
import { useProductsAndFilters } from "components/SegmentsSidebar/hooks"

import { useMarketplaceOptions } from "hooks"

import { checkIsArray } from "utils/arrayHelpers"
import l from "utils/intl"

import { DashboardFiltersFormType } from "components/SegmentsSidebar/components/Filters/FiltersTypes"

import { MarketplaceOption } from "types"

export const useMarketplaceIdFilter = () => {
  const form = useFormContext<DashboardFiltersFormType>()
  const [seller_id] = useWatch({
    control: form.control,
    name: ["seller_id"],
  })

  const { isLoadingProductsOrFilters } = useProductsAndFilters()
  const { getMarketplaceOptions } = useMarketplaceOptions()

  const { disabledOptions, renderSelectedCount } = useMemo(() => {
    const marketplacesOptions = getMarketplaceOptions(seller_id)
    const options = checkIsArray(marketplacesOptions)
      ? [...marketplacesOptions].sort(
          ({ country: prevCountry }, { country: nextCountry }) =>
            prevCountry.localeCompare(nextCountry),
        )
      : []

    const disabledOptions: MarketplaceOption[] = options.map((option) => ({
      ...option,
      disabled: isLoadingProductsOrFilters,
    }))

    const renderSelectedCount = (count: number): string =>
      `${count} ${l("selected")}`

    return {
      disabledOptions,
      renderSelectedCount,
    }
  }, [getMarketplaceOptions, isLoadingProductsOrFilters, seller_id])

  return {
    type: "select",
    name: "marketplace_id",
    inputProps: {
      label: l("Marketplace"),
      options: disabledOptions,
      isDisabled: isLoadingProductsOrFilters,
      prefixIcons: [
        {
          name: "icnFlag",
          color: "--color-icon-static",
        },
      ],
      isMultiSelect: true,
      hasClearIcon: true,
      tagsMode: "count",
      renderSelectedCount,
      renderOption: MarketplacesOption,
    },
    gridItemProps: {
      always: 12,
    },
  }
}
