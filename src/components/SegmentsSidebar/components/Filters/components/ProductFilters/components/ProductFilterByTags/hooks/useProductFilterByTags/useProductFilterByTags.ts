import { useSelector } from "react-redux"

import {
  productTagsAsOptionsSelector,
  productTagsStatusSelector,
} from "selectors/productTagsSelectors"

import { ASYNC_STATUSES } from "constants/async"

import { AsyncStatus } from "types"

export const useProductFilterByTags = () => {
  const productTagsAsOptions = useSelector(productTagsAsOptionsSelector)
  const productTagsStatus = useSelector(
    productTagsStatusSelector,
  ) as AsyncStatus

  return {
    options: productTagsAsOptions,
    isTagsLoading: productTagsStatus === ASYNC_STATUSES.PENDING,
  }
}
