import React, { use<PERSON><PERSON>back, useEffect, useMemo, useState } from "react"
import { useFormContext, useWatch } from "react-hook-form"
import debounce from "lodash/debounce"

import { PRODUCT_IDS_KEYS_LIST } from "components/SegmentsSidebar/components/Filters/components/ProductFilters/components/ProductFilterByUniqueIds/constants"
import { ProductFilterSelectOption } from "components/SegmentsSidebar/components/Filters/components/ProductFilters/components/ProductFilterSelectOption"
import { ProductFilterSimpleSelectOption } from "components/SegmentsSidebar/components/Filters/components/ProductFilters/components/ProductFilterSimpleSelectOption"
import { useProductFilterDropdownMenu } from "components/SegmentsSidebar/components/Filters/components/ProductFilters/hooks"
import {
  buildProductFilterOptions,
  makeOptionsUnique,
} from "components/SegmentsSidebar/components/Filters/components/ProductFilters/utils"
import {
  useCompareForm,
  useFetchProducts,
  useProductsAndFilters,
} from "components/SegmentsSidebar/hooks"

import l from "utils/intl"

import {
  DashboardFiltersFormType,
  ProductFiltersExtraFormKeys,
} from "components/SegmentsSidebar/components/Filters/FiltersTypes"

import { ProductOptionType, ProductType } from "types/store/salesHistoryReducer"

import {
  CustomKeyToDisplayType,
  CustomValueToDisplay,
  UseProductFilterByIdProps,
} from "./useProductFilterByIdTypes"
import { IsTogglePreventedParams } from "@develop/fe-library/dist/lib/components/Select/SelectTypes"

export const useProductFilterById = ({ id }: UseProductFilterByIdProps) => {
  const idHas = `has_${id}` as ProductFiltersExtraFormKeys
  const form = useFormContext<DashboardFiltersFormType>()
  const [idValue, idValueVisible, productId] = useWatch({
    control: form.control,
    name: [id, idHas, "productId"],
  })

  const {
    isLoadingProductsOrFilters,
    products,
    isProductsIdle,
    isProductsLoading,
    isProductsFulfilledOrRejected,
    findProduct,
    hasProducts,
    handleClearProducts,
  } = useProductsAndFilters()
  const { renderProductDropdownMenu } = useProductFilterDropdownMenu()
  const { fetchProducts } = useFetchProducts()
  const { getIsAnyProductFieldInForm } = useCompareForm()

  const options: ProductOptionType[] = useMemo(() => {
    const selectedProduct = findProduct(idValue, id)

    const productsForOptions: ProductType[] = selectedProduct
      ? [selectedProduct]
      : products

    return (
      productsForOptions
        .map(
          buildProductFilterOptions({
            valueKey: id,
            labelKey: id,
          }),
        )
        .filter(Boolean) as unknown as ProductOptionType[]
    ).reduce<ProductOptionType[]>(makeOptionsUnique, [])
  }, [findProduct, id, idValue, products])

  const [searchValue, setSearchValue] = useState("")

  //** Clear selected 'id' if has no options but selected */
  useEffect(() => {
    const isIdValueHidden: boolean = !idValueVisible
    const isSearchValue: boolean = searchValue.trim().length > 0

    if (isIdValueHidden || isSearchValue) {
      return
    }

    const selectedId = options.find(
      (option) => String(option.value) === String(idValue),
    )

    if (selectedId) {
      return
    }

    form.setValue(id, "")
    form.setValue(idHas, false)
  }, [options, idValue, searchValue])

  const searchProduct = useCallback(
    (searchValue: string) => {
      const values = form.getValues()

      fetchProducts({
        values,
        searchValue: {
          key: id,
          value: searchValue,
        },
      })
    },
    [fetchProducts, form, id],
  )

  const searchProductDebounced = useCallback(debounce(searchProduct, 500), [])

  const handleSearchValueChange = (value: string): void => {
    setSearchValue(value.trim())
  }

  const handleIsTogglePrevented = useCallback(
    ({ open }: IsTogglePreventedParams): boolean => {
      return open
    },
    [],
  )

  const clearProducts = useCallback(() => {
    const values = form.getValues()

    const isAnyProductFieldInForm = getIsAnyProductFieldInForm(values)

    if (isAnyProductFieldInForm) {
      return
    }

    handleClearProducts()
  }, [form, getIsAnyProductFieldInForm, handleClearProducts])

  const handleBlur = useCallback(() => {
    clearProducts()
  }, [clearProducts])

  useEffect(() => {
    const isAllowedToSearch: boolean =
      searchValue.trim().length >= 3 && !products?.length

    if (isAllowedToSearch) {
      if (hasProducts) {
        return
      }

      searchProductDebounced(searchValue)

      return
    }

    const isAllowedToClear: boolean = searchValue.trim().length === 0

    if (isAllowedToClear) {
      clearProducts()
    }
  }, [searchValue])

  useEffect(() => {
    return () => {
      searchProductDebounced.cancel()
    }
  }, [searchProductDebounced])

  const renderOption = useCallback(
    ({ option }: { option: ProductOptionType }) => {
      const isAsin: boolean = ["asin"].includes(id)
      const customKeyToDisplay: CustomKeyToDisplayType = !isAsin
        ? id
        : undefined
      const customValueToDisplay: CustomValueToDisplay = !isAsin
        ? id.toUpperCase()
        : undefined

      if (id === "sku") {
        return (
          <ProductFilterSimpleSelectOption
            customKeyToDisplay={customKeyToDisplay!}
            customValueToDisplay={customValueToDisplay!}
            option={option}
            searchValue={searchValue}
          />
        )
      }

      return (
        <ProductFilterSelectOption
          customKeyToDisplay={customKeyToDisplay}
          customValueToDisplay={customValueToDisplay}
          isAsin={isAsin}
          isMarketplaceHidden={PRODUCT_IDS_KEYS_LIST.includes(id)}
          option={option}
          searchValue={searchValue}
        />
      )
    },
    [id, searchValue],
  )

  const isDisabled: boolean = !!productId || isLoadingProductsOrFilters
  const isOptionsEmpty: boolean = !options.length
  const isIdle: boolean = isProductsIdle && (!idValue || isOptionsEmpty)
  const label: string = id.toUpperCase()
  const optionHeight: number = ["sku"].includes(id) ? 40 : 60

  return {
    items: [
      {
        type: "checkbox",
        name: idHas,
        inputProps: {
          label,
          disabled: isDisabled,
        },
        gridItemProps: {
          always: 12,
        },
      },
      {
        type: "select",
        name: id,
        inputProps: {
          label: l("Search"),
          options,
          hasClearIcon: true,
          hasChevronIcon: false,
          hasSearch: true,
          hasSearchIcon: false,
          optionHeight,
          searchValue,
          isGlobal: true,
          maxVisibleOptions: 5,
          isDisabled,
          isTogglePrevented: handleIsTogglePrevented,
          renderOption,
          renderDropdownMenu: renderProductDropdownMenu({
            title: id.toUpperCase(),
            isIdle,
            isLoading: isProductsLoading,
            isEmpty: isProductsFulfilledOrRejected && isOptionsEmpty,
          }),
          onSearch: handleSearchValueChange,
          onBlur: handleBlur,
        },
        gridItemProps: {
          always: 12,
        },
        isVisible: !!idValueVisible,
      },
    ],
  }
}
