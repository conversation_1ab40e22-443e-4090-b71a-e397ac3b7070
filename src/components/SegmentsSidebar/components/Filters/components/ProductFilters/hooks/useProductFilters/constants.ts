import { DashboardFiltersParams } from "components/SegmentsSidebar/components/Filters/FiltersTypes"

import { It<PERSON><PERSON><PERSON> } from "@develop/fe-library/dist/lib/components/Collapse/CollapseTypes"

export const PRODUCT_FILTERS_KEYS = {
  PRODUCT_ID: "productId",
  UNIQUE_IDS: "unique_ids",
  BRAND: "brand",
  PRODUCT_TYPE: "product_type",
  STOCK_TYPE: "stock_type",
  MANUFACTURER: "manufacturer",
  PARENT_ASIN: "parent_asin",
  TAGS: "tags",
} as const

export const PRODUCT_FILTERS_SECTIONS_KEYS = {
  [PRODUCT_FILTERS_KEYS.PRODUCT_ID]: ["productId"],
  [PRODUCT_FILTERS_KEYS.UNIQUE_IDS]: ["asin", "sku", "ean", "upc", "isbn"],
  [PRODUCT_FILTERS_KEYS.BRAND]: ["brand"],
  [PRODUCT_FILTERS_KEYS.PRODUCT_TYPE]: ["product_type"],
  [PRODUCT_FILTERS_KEYS.STOCK_TYPE]: ["stock_type"],
  [PRODUCT_FILTERS_KEYS.MANUFACTURER]: ["manufacturer"],
  [PRODUCT_FILTERS_KEYS.PARENT_ASIN]: ["parent_asin"],
  [PRODUCT_FILTERS_KEYS.TAGS]: ["tags"],
} as const

export const PRODUCT_FILTERS_ACTIVE_FILTERS: Record<
  ItemKey,
  (keyof DashboardFiltersParams)[]
> = {
  productId: [
    "productASIN",
    "productSku",
    "productSeller",
    "productMarketplace",
    "productId",
    "productRepricerId",
  ],
  unique_ids: ["asin", "sku", "ean", "upc", "isbn"],
  brand: ["brand"],
  product_type: ["product_type"],
  stock_type: ["stock_type"],
  manufacturer: ["manufacturer"],
  parent_asin: ["parent_asin"],
  tags: ["tags"],
}
