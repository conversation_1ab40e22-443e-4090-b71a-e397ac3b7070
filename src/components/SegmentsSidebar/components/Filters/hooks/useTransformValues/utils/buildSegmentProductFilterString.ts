import { getObjectEntries } from "@develop/fe-library/dist/utils"

import { SegmentOptionType } from "components/SegmentsSidebar/components/Segments/SegmentsTypes"

import { ProductFiltersMultipleType } from "types/store/salesHistoryReducer"

export const buildSegmentProductFilterString = (
  values: string[] | undefined,
  filters: ProductFiltersMultipleType,
): string => {
  if (!values?.length) {
    return ""
  }

  const OPTION_VALUE = 1

  const options: SegmentOptionType[] = values.map((value) => ({
    value,
    label:
      getObjectEntries(filters).find(([_, filter]) => filter.key === value)?.[
        OPTION_VALUE
      ]?.value || value,
  }))

  return JSON.stringify(options)
}
