import React, { ReactNode } from "react"
import { useForm, useWatch } from "react-hook-form"
import { Box, FormItems, Modal, Typography } from "@develop/fe-library"
import { buildFormSubmitFailureCallback } from "@develop/fe-library/dist/utils"

import { useSegments } from "components/SegmentsSidebar/hooks"

import l from "utils/intl"
import { checkIsFunction } from "utils/validationHelper"

import { SegmentType } from "types/store/segments"

import {
  FiltersSaveAsSegmentFormType,
  FiltersSaveAsSegmentModalProps,
} from "./FiltersSaveAsSegmentModalTypes"

export const FiltersSaveAsSegmentModal = ({
  values,
  title = "Save segment",
  onOk,
  onClose,
}: FiltersSaveAsSegmentModalProps) => {
  const { getAppliedSegment, segmentToEdit } = useSegments()

  const appliedSegment = getAppliedSegment()

  const segment: SegmentType | undefined = segmentToEdit || appliedSegment

  const form = useForm<FiltersSaveAsSegmentFormType>({
    defaultValues: {
      asNew: false,
      name: segment?.name,
    },
    mode: "onChange",
    reValidateMode: "onChange",
  })

  const [name, asNew] = useWatch({
    control: form.control,
    name: ["name", "asNew"],
  })

  const handleSaveAs = form.handleSubmit((formData) => {
    if (checkIsFunction(onOk)) {
      const { setError } = form

      const handleFailure =
        buildFormSubmitFailureCallback<FiltersSaveAsSegmentFormType>({
          setError,
        })

      onOk({
        values,
        name: formData.name,
        id: asNew ? undefined : segment?.id,
        // @ts-expect-error
        onFailure: handleFailure,
      })
    }
  })

  const handleStopPropagation = (e: React.MouseEvent) => {
    e.stopPropagation()
  }

  const isNameFieldDisabled: boolean = !!segment?.id && !!name && !asNew

  const validateFieldLength = (value: string) => {
    if (value.length < 2) {
      return l("Enter at least 3 characters")
    }

    return true
  }

  return (
    <Modal
      visible
      cancelButtonText={l("Cancel")}
      okButtonText={l("Save")}
      title={l(title)}
      width="--modal-size-s"
      wrapperProps={{
        onClick: handleStopPropagation,
        onMouseDown: handleStopPropagation,
      }}
      onCancel={onClose}
      onOk={handleSaveAs}
    >
      <Box flexDirection="column" gap="m">
        {segment?.id ? (
          <>
            <Typography variant="--font-body-text-7">
              {l(
                "The <b>Segment</b> will be overwritten. You can save it as a new.",
                {
                  b: (chunks: ReactNode) => <b>{chunks}</b>,
                },
              )}
            </Typography>
          </>
        ) : null}

        <FormItems
          // @ts-expect-error
          form={form}
          gridContainerProps={undefined}
          items={[
            {
              type: "switch",
              name: "asNew",
              inputProps: {
                label: l("Save it as a new"),
              },
              gridItemProps: {
                xs: 12,
              },
              isVisible: !!segment?.id,
            },
          ]}
        />

        <FormItems
          // @ts-expect-error
          form={form}
          gridContainerProps={undefined}
          items={[
            {
              type: "text",
              name: "name",
              inputProps: {
                label: l("Segment name"),
                isDisabled: isNameFieldDisabled,
              },
              rules: {
                required: true,
                validate: validateFieldLength,
              },
              gridItemProps: {
                always: 12,
              },
            },
          ]}
        />
      </Box>
    </Modal>
  )
}
