import { HandleSaveAsProps } from "../Filters/components/FiltersFooter/hooks/useFiltersFooter/useFiltersFooterTypes"
import { DashboardFiltersFormType } from "../Filters/FiltersTypes"

export type FiltersSaveAsSegmentFormType = {
  asNew: boolean
  name: string
}

export type FiltersSaveAsSegmentModalProps = {
  values: DashboardFiltersFormType
  title?: string
  onOk: (props: HandleSaveAsProps) => void
  onClose: () => void
}
