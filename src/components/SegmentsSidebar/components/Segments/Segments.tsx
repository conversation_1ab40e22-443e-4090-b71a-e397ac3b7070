import React from "react"
import { Box, Typography } from "@develop/fe-library"

import { useSegments } from "components/SegmentsSidebar/hooks"

import l from "utils/intl"

import { TabContentHeader } from "../TabContentHeader"
import { SegmentsProps } from "./SegmentsTypes"
import { CollapsableSegments } from "./components"

export const Segments = ({
  activeTab,
  onCloseTab,
  onSegmentApply,
  onSegmentEdit,
}: SegmentsProps) => {
  const { segments } = useSegments()

  return (
    <>
      <TabContentHeader onCloseTab={onCloseTab}>
        <Typography variant="--font-headline-5">{activeTab.label}</Typography>
      </TabContentHeader>

      <Box flexDirection="column" overflowY="auto">
        {segments?.length > 0 ? (
          <CollapsableSegments
            onSegmentApply={onSegmentApply}
            onSegmentEdit={onSegmentEdit}
          />
        ) : (
          <Box padding="l">
            <Typography variant="--font-body-text-9">
              {l("No segments found")}
            </Typography>
          </Box>
        )}
      </Box>
    </>
  )
}
