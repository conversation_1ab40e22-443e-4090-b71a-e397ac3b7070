import React, { ReactNode, useCallback } from "react"
import { Box, Button, Tag, Typography } from "@develop/fe-library"

import { useSegments } from "components/SegmentsSidebar/hooks"

import { checkIsArray } from "utils/arrayHelpers"
import l from "utils/intl"

import { useTransformSegment } from "./hooks"

import { CollapsableSegmentContentProps } from "./CollapsableSegmentContentTypes"
import { UseTransformSegmentCategoryType } from "./hooks/useTransformSegment/useTransformSegmentTypes"

export const CollapsableSegmentContent = ({
  segment,
  isGroupAccountAbsent = false,
  onApplySegment,
}: CollapsableSegmentContentProps) => {
  const { segmentId } = useSegments()
  const { segmentsFormatted, seller_id } = useTransformSegment(segment)

  const handleSegmentApply = useCallback(() => {
    onApplySegment(segment.id)
  }, [onApplySegment, segment.id])

  const SegmentContent = ({
    content,
    hint,
  }: {
    content: UseTransformSegmentCategoryType
    hint?: ReactNode
  }) => {
    const isTags: boolean = content.id === "tags" && checkIsArray(content.value)

    return (
      <Box flexDirection="column" gap="s">
        <Typography color="--color-text-main" variant="--font-body-text-9">
          {content.key}
        </Typography>

        {isTags ? (
          <Box flexWrap="wrap" gap="s">
            {/* 
            @ts-expect-error */}
            {content.value?.map((tag) => (
              <Tag
                key={tag.value}
                color={tag.color}
                isDefault={false}
                name={tag.label}
                size="s"
              />
            ))}
          </Box>
        ) : null}

        {!isTags ? (
          <Typography
            color="--color-text-second"
            variant="--font-body-text-9"
            style={{
              wordBreak: "break-word",
            }}
          >
            {content.value}
          </Typography>
        ) : null}

        {hint}
      </Box>
    )
  }

  const groupAccountHint = isGroupAccountAbsent ? (
    <Typography color="--color-text-error" variant="--font-body-text-9">
      {l("This Group or Account has been removed.")}
    </Typography>
  ) : null

  const isDisabled: boolean = segment.id === segmentId || isGroupAccountAbsent

  return (
    <Box flexDirection="column" gap="m" maxWidth="100%">
      <SegmentContent content={seller_id} hint={groupAccountHint} />

      {segmentsFormatted.map((content) => (
        <SegmentContent key={content?.key} content={content} />
      ))}

      <Button disabled={isDisabled} width="100%" onClick={handleSegmentApply}>
        {l("Apply")}
      </Button>
    </Box>
  )
}
