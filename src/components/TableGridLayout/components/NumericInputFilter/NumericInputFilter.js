import React from 'react'
import PropTypes from 'prop-types'
import { Input } from 'antd'
import { getLnSign } from 'utils/localeNumber'

class NumericInputFilterView extends React.Component {
  constructor(props) {
    super(props)
    const { value, defaultValue = '' } = props

    this.state = {
      value: `${value}` === 'undefined' ? defaultValue : value,
    }
  }

  ln = number => {
    if (number) {
      number += ''
      return getLnSign() === '.'
        ? number.replace(/,/g, '.')
        : number.replace(/\./g, ',')
    }
    return number
  }

  onChange = ({ target: { value } }) => {
    const { value: propsValue } = this.props
    const { onChange } = this.hidenInput.props

    value = (value || '').replace(',', '.')
    value = value.replace(/\s/g, '')
    onChange && onChange({ target: { value } })
    if (typeof propsValue === 'undefined') {
      this.setState({ value })
    } else {
      this.setState({ value: '' })
    }
  }

  onBlur = () => {
    const { value: propsValue, onBlur } = this.props
    const { value: stateValue } = this.state
    const value = `${propsValue || stateValue || ''}`
    onBlur && onBlur(value)
  }

  focus() {
    const { input } = this.showedInput
    if (input) {
      input.focus()
    }
  }

  componentDidUpdate(prevProps) {
    const { value: prevValue } = prevProps
    const { value } = this.props
    if (prevValue && !value) {
      this.setState({ value })
    }
  }

  render() {
    const { value } = this.props
    const { value: stateValue } = this.state
    const valueLn = this.ln(value)
    const stateValueLn = this.ln(stateValue)

    return (
      <>
        <Input
          ref={thisInput => (this.showedInput = thisInput)}
          {...this.props}
          name={''}
          onChange={this.onChange}
          onBlur={this.onBlur}
          value={valueLn || stateValueLn}
        />
        <Input
          ref={thisInput => (this.hidenInput = thisInput)}
          {...this.props}
          style={{ display: 'none' }}
          value={value || stateValue}
        />
      </>
    )
  }
}

NumericInputFilterView.propTypes = {
  defaultValue: PropTypes.string,
  onBlur: PropTypes.func,
  onChange: PropTypes.func,
  value: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
}

NumericInputFilterView.defaultProps = {
  type: 'text',
  defaultValue: '',
}

export default NumericInputFilterView
