@import 'assets/styles/variables.scss';

.container {
  position: relative;
  z-index: 2;
}
.grid-filter-sorted {
  & .ant-input {
    font-size: 12px;
    padding-left: 22px !important;
  }
  & .ant-select-selection__rendered {
    font-size: 12px;
    margin-left: 22px !important;
  }
}
.grid-filter {
  &.grid-filter-with-placeholder {
    .ant-input,
    .ant-input::placeholder,
    .ant-select-selection__placeholder {
      color: $main_bg !important;
    }
  }
  .ant-select {
    width: 100%;
  }
  .ant-select-single .ant-select-selector .ant-select-selection-search {
    left: 22px;
  }
  .ant-select-selection-overflow {
    display: none;
  }
  &.grid-filter-multySelect {
    display: block;
    .ant-select-selection__rendered {
      margin-right: 20px;
    }
  }
  .ant-select-clear,
  .ant-select-selection__clear {
    right: 5px;
  }

  .ant-select-selection__clear {
    position: absolute;
    top: -1px;
    cursor: pointer;
  }

  .ant-select-selection-item.ant-select-selection-item {
    font-size: 12px;
    padding-left: 12px;
    padding-right: 10px;
  }

  .ant-select-show-search {
    .ant-select-selection-item.ant-select-selection-item {
      text-align: left;
    }
  }

  & .ant-input {
    padding: 6px 5px;
    word-break: normal;
    white-space: nowrap !important;
  }
  .ant-calendar-picker {
    max-height: 32px;
    display: block;
  }
  .grid-filter-multiselect-icon {
    font-size: 12px;
    pointer-events: none;
    position: absolute;
    right: 6px;
    top: 10px;
    z-index: 2;
    transition: transform 0.3s;
  }
  &.grid-filter-focused .grid-filter-multi-select-icon {
    transform: rotate(180deg);
  }
}
.grid-filter {
  &:hover {
    .ant-input,
    .ant-select-selection {
      border-color: $border_active;
    }
  }
}
.is-resizing {
  .grid-filter {
    &:hover {
      .ant-input,
      .ant-select-selection {
        border-color: $border_main;
      }
    }
  }
}
