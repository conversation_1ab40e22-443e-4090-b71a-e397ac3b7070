import React, { useState } from "react"
import XLSX from "xlsx"
import PropTypes from "prop-types"
import { useSelector } from "react-redux"
import { Typography, Modal, Box, Select } from "@develop/fe-library"
import { getUrlSearchParams } from "@develop/fe-library/dist/utils"

import l from "utils/intl"
import { convertToLocalDate } from "utils/dateConverter"

import { UNIT_TYPES } from "constants/widgets"

import { getWidgetStatisticsSelector } from "selectors/salesHistorySelectors"

import { EXPORT_AVAILABLE_FORMATS } from "./constants"

import styles from "./exportChart.module.scss"

export const ExportStatisticsModal = ({ onCancel }) => {
  const [exportFormat, setExportFormat] = useState(
    EXPORT_AVAILABLE_FORMATS.csv.value,
  )

  const widgetStatistics = useSelector(getWidgetStatisticsSelector)
  const urlParams = getUrlSearchParams({
    locationSearch: document.location.search,
  })

  const handleExportData = () => {
    const fileName = `${l("widget")}_${l("export")}_${urlParams.from}_${
      urlParams.to
    }.${exportFormat}`
    const dateRangeColumnName = l("Date range")
    const dateRangeColumnValue = `${convertToLocalDate(
      urlParams.from,
    )} - ${convertToLocalDate(urlParams.to)}`

    const jsonSheet = widgetStatistics?.reduce(
      (result, { name, amount, type }) => {
        const cellAmount = type === UNIT_TYPES.pct ? `${amount}%` : amount

        return [
          [...result[0], l(name)],
          [...result[1], cellAmount],
        ]
      },
      [[dateRangeColumnName], [dateRangeColumnValue]],
    )

    const ws = XLSX.utils.aoa_to_sheet(jsonSheet)
    const wb = XLSX.utils.book_new()

    XLSX.utils.book_append_sheet(wb, ws, "Table")
    XLSX.writeFile(wb, fileName)

    onCancel()
  }

  const handleExportFormatChange = ({ value }) => {
    setExportFormat(value)
  }

  return (
    <Modal
      title={l("Export widget data")}
      onCancel={onCancel}
      visible
      cancelButtonText={l("Cancel")}
      okButtonText={l("Export")}
      onOk={handleExportData}
    >
      <Box flexDirection="column" gap="l">
        <Typography
          variant="--font-body-text-7"
          color="--color-text-main"
          className={styles.description}
        >
          {l("Choose how you want to export your data")}
        </Typography>

        <Select
          defaultValue={exportFormat}
          onChange={handleExportFormatChange}
          getPopupContainer={(trigger) => trigger.parentElement}
          options={[
            EXPORT_AVAILABLE_FORMATS.xlsx,
            EXPORT_AVAILABLE_FORMATS.csv,
          ]}
        />
      </Box>
    </Modal>
  )
}

ExportStatisticsModal.propTypes = {
  onCancel: PropTypes.func.isRequired,
}
