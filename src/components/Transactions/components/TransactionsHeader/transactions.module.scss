@import "assets/styles/variables";

.filterContainer {
  :global(.marketplace-selector-container),
  :global(.groups-selector-container),
  :global(.groups-selector),
  .datePickerContainer,
  .currencySelector,
  .transactionLevelsSelectWrapper {
    max-width: 100%;
    width: 100%;
    margin: 0;
    min-width: unset;

    @media screen and (max-width: $sm) {
      margin: 0;
    }
  }
}

.datePickerContainer {
  > div {
    margin-bottom: 0;
  }
}

.filterRightContainer {
  button {
    flex-shrink: 0;
  }
}

.transactionLevelsSelectWrapper {
  > div {
    width: 100%;
  }
}
