import React from "react"

import ln from "utils/localeNumber"

import {
  ProductToLookAtCustomBarComponentProps,
  ProductToLookAtCustomBarProps,
} from "./ProductToLookAtCustomBarTypes"

export const ProductToLookAtCustomBar =
  ({
    currency_code,
    isMobile,
    tooltipProductId,
  }: ProductToLookAtCustomBarComponentProps) =>
  ({ x, y, width, height, payload }: ProductToLookAtCustomBarProps) => {
    const {
      expenses_amount = 0,
      revenue_amount = 0,
      estimated_profit_amount = 0,
      product_id,
    } = payload || {}

    const outerFillColor: string =
      tooltipProductId === Number(product_id) ? "#cfdcf3" : "#e7eefa"

    const innerWidth: number =
      expenses_amount !== 0
        ? Math.abs(width * (revenue_amount / expenses_amount))
        : 0

    const innerY = y + 3

    const labelX = isMobile ? "26px" : "100%"
    const labelY = y + height / 2 + (isMobile ? 19 : 0)

    const formatValue = (value: number): string =>
      ln(value, 2, {
        currency: currency_code,
      })

    const OUTER_HEIGHT = 16
    const INNER_HEIGHT = 10

    return (
      <g>
        <rect
          fill={outerFillColor}
          height={OUTER_HEIGHT}
          rx={2}
          ry={2}
          width={Math.max(0, width)}
          x={x}
          y={y}
        />
        <rect
          fill="#3b5cd2"
          height={INNER_HEIGHT}
          rx={2}
          ry={2}
          width={Math.max(0, innerWidth)}
          x={x}
          y={innerY}
        />

        <text
          fill="#000"
          fontSize={12}
          textAnchor={isMobile ? "start" : "end"}
          x={labelX}
          y={labelY}
        >
          -{formatValue(Math.abs(estimated_profit_amount))}
        </text>
      </g>
    )
  }
