import React from "react"

import {
  ProductToLookAtCustomYAxisTickComponentProps,
  ProductToLookAtCustomYAxisTickProps,
} from "./ProductToLookAtCustomYAxisTickTypes"

export const ProductToLookAtCustomYAxisTick =
  ({ isMobile }: ProductToLookAtCustomYAxisTickComponentProps) =>
  ({ x, y, index }: ProductToLookAtCustomYAxisTickProps) => {
    return (
      <g transform={`translate(${x},${y})`}>
        <text
          fill="#666"
          fontSize={12}
          textAnchor="start"
          x={-16}
          y={isMobile ? -2 : 3}
        >
          {index + 1}
        </text>
      </g>
    )
  }
