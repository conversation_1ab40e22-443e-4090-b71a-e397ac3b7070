import { useMemo, useState } from "react"
import { Option } from "@develop/fe-library"
import * as htmlToImage from "html-to-image"
import jsPDF from "jspdf"

import { DashboardFiltersParams } from "components/SegmentsSidebar/components/Filters/FiltersTypes"

import { useUrlParams } from "hooks"

import l from "utils/intl"

import { DOWNLOAD_CHART_FORMATS } from "constants/chart"

import { DownloadFormatsList } from "../../ProductToLookAtDownloadModalTypes"

import { UseProductToLookAtDownloadModalProps } from "./useProductToLookAtDownloadModalTypes"

export const useProductToLookAtDownloadModal = ({
  isVisible,
  onCancel,
  chartRef,
}: UseProductToLookAtDownloadModalProps) => {
  const { urlParams } = useUrlParams<DashboardFiltersParams>()

  const visible = useMemo(() => isVisible, [isVisible])

  const [fileFormat, setFileFormat] = useState<DownloadFormatsList["value"]>(
    DOWNLOAD_CHART_FORMATS.PNG.value,
  )

  const handleFormatChange = ({ value }: Option) => {
    setFileFormat(value as DownloadFormatsList["value"])
  }

  const handleDownload = () => {
    if (!chartRef?.current) {
      return
    }

    const fileName = `${l("negative")}_${l("margin")}_${l("products")}")}_${
      urlParams.from
    }_${urlParams.to}.${fileFormat}`

    const convertToPng = () => {
      htmlToImage
        .toPng(chartRef.current as HTMLElement, { skipFonts: true })
        .then((dataUrl) => {
          const link = document.createElement("a")

          link.href = dataUrl
          link.download = fileName
          link.click()
        })
        .finally(() => {
          onCancel()
        })
    }

    const convertToPdf = () => {
      htmlToImage
        .toCanvas(chartRef.current as HTMLElement, { skipFonts: true })
        .then((canvas) => {
          const doc = new jsPDF("landscape")
          const width = doc.internal.pageSize.getWidth()
          const height = doc.internal.pageSize.getHeight()
          const widthRatio = width / canvas.width
          const heightRatio = height / canvas.height
          const ratio = widthRatio > heightRatio ? heightRatio : widthRatio

          doc.addImage(
            canvas.toDataURL("image/png", 1.0),
            "PNG",
            0,
            0,
            canvas.width * ratio,
            canvas.height * ratio,
          )

          doc.save(fileName)
        })
        .catch((error) => {
          console.error("Error while converting chart to PDF", error)
        })
        .finally(() => {
          onCancel()
        })
    }

    switch (fileFormat) {
      case DOWNLOAD_CHART_FORMATS.PNG.value:
        convertToPng()
        break
      case DOWNLOAD_CHART_FORMATS.PDF.value:
        convertToPdf()
        break
      default:
        console.error("Unknown file format")
    }
  }

  return {
    visible,
    fileFormat,
    handleFormatChange,
    handleDownload,
  }
}
