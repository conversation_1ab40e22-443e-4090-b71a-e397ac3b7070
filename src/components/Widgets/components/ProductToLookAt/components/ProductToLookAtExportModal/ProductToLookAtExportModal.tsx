import React from "react"
import { Box, Modal, Select, Typography } from "@develop/fe-library"

import l from "utils/intl"

import { EXPORT_OPTIONS } from "./hooks/useProductToLookAtExportModal/constants"

import { useProductToLookAtExportModal } from "./hooks/useProductToLookAtExportModal/useProductToLookAtExportModal"

import { ProductToLookAtExportModalProps } from "./ProductToLookAtExportModalTypes"

export const ProductToLookAtExportModal = ({
  isVisible,
  onCancel,
}: ProductToLookAtExportModalProps) => {
  const { fileFormat, handleFormatChange, handleStartExport } =
    useProductToLookAtExportModal({ onCancel })

  return (
    <Modal
      cancelButtonText={l("Cancel")}
      okButtonText={l("Export")}
      title={l("Export data")}
      visible={isVisible}
      onCancel={onCancel}
      onOk={handleStartExport}
    >
      <Box flexDirection="column" gap="l">
        <Typography color="--color-text-main" variant="--font-body-text-7">
          {l(
            "Choose how you want to export your Negative margin products data",
          )}
        </Typography>

        <Select
          defaultValue={fileFormat}
          options={EXPORT_OPTIONS}
          onChange={handleFormatChange}
        />
      </Box>
    </Modal>
  )
}
