import React from "react"
import { Box, Modal, Pagination, SimpleTable } from "@develop/fe-library"

import { ProductToLookAtFullscreenModalChart } from "./components"

import { useProductToLookAtFullscreenModal } from "./hooks/useProductToLookAtFullscreenModal"

import type { ProductToLookAtFullscreenModalProps } from "./ProductToLookAtFullscreenModalTypes"

export const ProductToLookAtFullscreenModal = ({
  title,
  isVisible,
  onCancel,
  onOpenDownloadModal,
  onOpenExportModal,
}: ProductToLookAtFullscreenModalProps) => {
  const {
    isWidgetLoading,

    columns,
    rows,

    currentPage,
    handleChangePage,
    pageSize,
    totalCount,

    sort,
    handleChangeSort,
  } = useProductToLookAtFullscreenModal()

  return (
    <Modal
      isWithoutBodyPadding
      isDraggable={false}
      title={title}
      visible={isVisible}
      width="--modal-size-xl"
      onClose={onCancel}
    >
      <ProductToLookAtFullscreenModalChart
        sort={sort}
        onChangeSort={handleChangeSort}
        onOpenDownloadModal={onOpenDownloadModal}
        onOpenExportModal={onOpenExportModal}
      />

      <Box display="block">
        <Box display="block" overflowX="auto" width="100%">
          <SimpleTable
            columns={columns}
            data={rows}
            hasOuterBorder={false}
            isLoading={isWidgetLoading}
          />
        </Box>

        <Box
          justify="center"
          padding="m"
          hasBorder={{
            top: true,
          }}
        >
          <Pagination
            currentPage={currentPage}
            isDisabled={isWidgetLoading}
            pageSizeLimit={pageSize}
            total={totalCount}
            onPageChange={handleChangePage}
          />
        </Box>
      </Box>
    </Modal>
  )
}
