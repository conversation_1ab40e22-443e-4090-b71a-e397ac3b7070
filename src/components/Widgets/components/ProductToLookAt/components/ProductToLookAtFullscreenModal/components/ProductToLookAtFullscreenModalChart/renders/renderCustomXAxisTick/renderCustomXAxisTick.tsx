import React from "react"

import { CustomAxisTickProps } from "types/Models/Charts"

export const renderCustomXAxisTick = ({
  x,
  y,
  width,
  index,
}: CustomAxisTickProps) => {
  return (
    <g transform={`translate(${x},${y})`}>
      <text
        dy={8}
        fill="var(--color-text-second)"
        fontSize="11"
        textAnchor="middle"
        width={width}
      >
        {index + 1}
      </text>
    </g>
  )
}
