import React from "react"
import PropTypes from "prop-types"
import { useSelector } from "react-redux"
import { Link } from "react-router-dom"

import { languageSelector } from "selectors/mainStateSelectors"

import { replaceLanguageCode } from "utils/replaceLanguageCode"

import { shouldOpenInNewTab } from "./newTabUrls"

const LinkAdapterHoc = (props) => {
  const language = useSelector(languageSelector)
  let { url: linkUrl, internal, itemId, ...rest } = props

  let aProps = { ...rest }

  if (
    shouldOpenInNewTab({
      url: linkUrl,
      language,
    })
  ) {
    aProps = { ...aProps, target: "_blank" }
  }

  linkUrl = replaceLanguageCode({
    url: linkUrl,
    language,
  })

  return internal ? (
    <Link {...props} to={linkUrl} internal={undefined} url={undefined} />
  ) : (
    // eslint-disable-next-line jsx-a11y/anchor-has-content
    <a {...aProps} href={linkUrl} />
  )
}

LinkAdapterHoc.propTypes = {
  internal: PropTypes.bool,
  url: PropTypes.string,
}

LinkAdapterHoc.defaultProps = {
  internal: false,
}

export default LinkAdapterHoc
