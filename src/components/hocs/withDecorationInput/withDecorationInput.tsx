// @ts-nocheck

import React from 'react'
import cn from 'classnames'
import { IconPopover } from '@develop/fe-library'

import { WithDecorationProps } from './index'

import './withDecorationInput.scss'

const withDecorationInput = <P extends object>(
  Component: React.ComponentType<P>,
  type = 'input',
) =>
  class extends React.Component<WithDecorationProps> {
    value: any = null
    input: any

    state = {
      outLineIsActive: false,
    }

    getOutLineIsActive = () => {
      const { withOutLineLabel } = this.props
      if (!withOutLineLabel) return
      if (Array.isArray(this.value)) {
        this.setState({ outLineIsActive: !!this.value?.length })
        return
      }
      this.setState({ outLineIsActive: this.value === 0 || !!this.value })
    }
    onBlur = (...args) => {
      this.props?.onBlur?.(...args)
      this.getOutLineIsActive()
    }

    onFocus = (...args) => {
      this.props?.onFocus?.(...args)
      this.setState({ outLineIsActive: true })
    }

    onChange = (e, ...args) => {
      this.props?.onChange?.(e, ...args)
      if (e?.target) this.value = e?.target.value
      if (type === 'select') {
        this.value = e
      }

      if (this.props?.autoCloseDropdown) {
        document?.activeElement?.blur?.()
      }
      this.getOutLineIsActive()
    }

    componentDidMount() {
      this.value = this.props.value
      this.getOutLineIsActive()
    }

    componentDidUpdate(prevProps: any) {
      if (prevProps.value !== this.props.value) {
        this.value = this.props.value
        this.getOutLineIsActive()
      }
    }

    render() {
      const {
        errorText,
        doneText,
        className,
        inputClassName,
        withOutLineLabel,
        noAnimationOutLineLabel,
        prefix,
        isErrorTooltip,
        postfix,
        ...props
      } = this.props

      const { outLineIsActive } = this.state

      return (
        <div
          className={cn(
            'sl-input-container',
            {
              'sl-input-with-error': !!errorText,
              'sl-input-done': !errorText && !!doneText,
              'sl-input-outline': withOutLineLabel,
              'active-outline': outLineIsActive,
              'no-animation': noAnimationOutLineLabel,
              disabled: !!this.props.disabled,
              'sl-input-with-prefix': !!prefix,
              'sl-input-with-postfix': !!postfix,
            },
            className,
          )}
        >
          <Component
            {...(props as P)}
            className={inputClassName}
            onBlur={this.onBlur}
            onFocus={this.onFocus}
            onChange={this.onChange}
            ref={this.input}
          />
          {!!errorText && isErrorTooltip && (
            <IconPopover
              name="icnExclamationCircle"
              className="sl-input-with-error__icon"
              content={'errorText'}
              size="--icon-size-3"
              color="--color-icon-error"
              placement="top"
            />
          )}
          {!!errorText && !isErrorTooltip && (
            <span className="sl-error-help-text">{errorText}</span>
          )}
          {!errorText && !!doneText && doneText !== '-' && (
            <IconPopover
              name="icnCheckCircle"
              className="sl-input-done__icon"
              content={doneText}
              placement="top"
              size="--icon-size-3"
              color="--color-icon-done"
            />
          )}
          {!!withOutLineLabel && (
            <div className="sl-input-outline__label">
              <span>{props?.placeholder}</span>
            </div>
          )}
          {!!prefix && <div className="sl-input__prefix">{prefix}</div>}
          {!!postfix && (
            <div className="sl-input__postfix-container">{postfix}</div>
          )}
        </div>
      )
    }
  }

export default withDecorationInput
