@import 'assets/styles/variables.scss';

.input-outline-box {
  position: relative;
  width: 100%;
  .input-outline-label {
    pointer-events: none;
    position: absolute;
    top: 0;
    left: 10px;
    right: 6px;
    color: $placeholder;
    font-size: 12px;
    font-weight: normal;
    font-family: 'Roboto', sans-serif;
    z-index: 2;
    display: flex;
    align-items: center;
    padding: 0px;
    margin-top: 1px;
    height: 30px;

    .input-outline-label-text {
      display: block;
      max-width: 100%;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }
  }
  &.select-input-outline {
    .input-outline-label {
      right: 20px;
    }
  }
  &.phone-input-outline {
    .input-outline-label {
      padding-left: 10px;
    }
    &.active .input-outline-label {
      padding-left: 0px;
    }
  }
  &.active {
    .input-outline-label {
      background: none;
      font-size: 10px;
      top: -8px;
      left: 4px !important;
      right: 4px !important;
      height: 10px;
      & .input-outline-label-text {
        color: $text_second;
        margin-top: 3px;
        background-color: $main_bg;
        display: inline-block;
        line-height: 10px;
        max-height: 20px;
        padding: 0px 4px;
        -webkit-touch-callout: none;
        user-select: none;
      }
    }
  }
  .ant-select-arrow {
    z-index: 5;
  }
  .ant-input::placeholder,
  .ant-select-selection__placeholder {
    color: $main_bg !important;
  }
}

.with-animation {
  transition: 0.3s;
  &.active {
    .input-outline-label {
      transition: 0.3s;
    }
    & .input-outline-label-text {
      transition: 0.1s;
    }
  }
}
