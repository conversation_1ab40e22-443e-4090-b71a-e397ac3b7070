import React, { useState, useEffect, useRef } from "react"
import cn from "classnames"
import { CopyToClipboard as ReactCopyToClipboard } from "react-copy-to-clipboard"
import { Box, Icon, Popover, Button, Typography } from "@develop/fe-library"

import l from "utils/intl"

import { CopyToClipboardProps } from "./CopyToClipboardTypes"

import styles from "./copyToClipboard.module.scss"

export const CopyToClipboard = ({
  text,
  iconSize = "--icon-size-3",
  iconColor = "--color-icon-clickable",
  display = "inline",
  component = "span",
  variant = "text",
  ...otherProps
}: CopyToClipboardProps) => {
  const [visible, setVisible] = useState(false)
  const timeout = useRef<NodeJS.Timeout>()

  const showPopover = (): void => {
    setVisible(true)
    timeout.current = setTimeout(() => setVisible(false), 2000)
  }

  const hidePopover = (): void => {
    setVisible(false)
    if (timeout.current) {
      clearTimeout(timeout.current)
    }
  }

  useEffect(() => {
    document.addEventListener("click", hidePopover)

    return () => {
      document.removeEventListener("click", hidePopover)

      if (timeout.current) {
        clearTimeout(timeout.current)
      }
    }
  }, [])

  const handleClick: React.MouseEventHandler<HTMLButtonElement> = (event) => {
    event.stopPropagation()

    if (visible) {
      hidePopover()
      setTimeout(() => showPopover(), 100)
    } else {
      showPopover()
    }
  }

  return (
    <Popover
      trigger="click"
      placement="topRight"
      content={
        <Box gap="m" align="center">
          <Icon
            name="icnCheckCircle"
            size="--icon-size-5"
            color="--color-icon-done"
          />
          <Typography variant="--font-body-text-7">
            {l("Copied to clipboard")}
          </Typography>
        </Box>
      }
      isVisible={visible}
    >
      <Box {...otherProps} component={component} display={display}>
        <ReactCopyToClipboard text={text}>
          <Button
            className={cn({ [styles.button]: variant === "text" })}
            iconOnly
            variant={variant}
            onClick={handleClick}
            icon="icnCopy"
            iconSize={iconSize}
            iconColor={iconColor}
            type="button"
          />
        </ReactCopyToClipboard>
      </Box>
    </Popover>
  )
}
