import React, { useState, useEffect, useCallback } from 'react'
import PropTypes from 'prop-types'
import cn from 'classnames'
import { DatePicker, TimePicker, Tabs, Radio } from 'antd'

import CalendarInputs from '../CalendarInputs/CalendarInputsView'
import CalendarItem from './CalendarItem'
import FormattedMessage from 'components/FormattedMessage'
import Typography from 'components/Typography'

import { getStringTimeZoneNumber } from 'utils/dateConverter'
import l from 'utils/intl'

import usePrevious from 'hooks/usePrevious'

import { TIME_FORMAT_WITH_SECONDS } from "constants/dateTime"

import './calendar.scss'

const { TabPane } = Tabs

const CalendarView = ({
  value,
  onChangeCalendar: propsOnChangeCalendar,
  onChangeInput,
  onPressEnter,
  onChangeInputSettings,
  onChangeLastFilterSettings,
  onChangeTimeZone,
  onFocusedInput,
  setFocusedInput,
  placeholder,
  className,
  format,
  localizedFormat,
  open,
  getInputsRef,
  showTime,
  defaultTab,
  mode,
  isRange,
  isFilter,
  inputSettings,
  lastFilterSettings,
  lastDate,
  focusedInput,
  minDaysRange,
  disabledDate,
  showTimeZoneToggle,
  isUTC,
  ...otherProps
}) => {
  const [momentDates, setMomentDate] = useState([null, null])
  const [activeTab, setActiveTab] = useState(null)
  const [renderKey, setRenderKey] = useState('1')
  const [activeMode, setActiveMode] = useState('date')
  const [stringTimeZone, setStringTimeZone] = useState('0')
  const prev = usePrevious({ activeTab, mode })

  useEffect(() => {
    setMomentDate(open ? value : [null, null])
    setStringTimeZone(getStringTimeZoneNumber())
  }, [value, open])

  useEffect(() => {
    if (!prev || prev.activeTab === activeTab || prev?.activeTab !== 'time')
      return

    setRenderKey(Math.random() + '')
    setActiveMode('date')
    setActiveTab(null)
  }, [activeTab, prev])

  useEffect(() => {
    setActiveTab(mode === 'time' ? mode : null)
    setActiveMode(mode || 'date')
  }, [mode])

  const onChangeCalendar = useCallback(
    momentDate => {
      if (inputSettings.isLastRange) {
        propsOnChangeCalendar(momentDate)
        return
      }

      if (focusedInput !== 'to') {
        setMomentDate([momentDate, momentDates[1]])
        propsOnChangeCalendar &&
          propsOnChangeCalendar([momentDate, momentDates[1]])
        if (inputSettings.activeDate === 'from-to') {
          setFocusedInput('to')
        } else {
          setFocusedInput(focusedInput)
        }
      } else {
        setFocusedInput(focusedInput)
        setMomentDate([momentDates[0], momentDate])
        propsOnChangeCalendar &&
          propsOnChangeCalendar([momentDates[0], momentDate])
      }
    },
    [
      momentDates,
      focusedInput,
      propsOnChangeCalendar,
      setFocusedInput,
      inputSettings,
    ]
  )

  const dateRender = useCallback(
    current => (
      <CalendarItem
        dateFrom={value[0]}
        dateTo={value[1]}
        current={current}
        inputType={inputSettings.type}
        isRange={inputSettings.isRange}
      />
    ),
    [value, inputSettings]
  )

  return (
    <div className={cn('date-picker-body', className)}>
      <div className="date-picker-calendar-input-box">
        <CalendarInputs
          getInputsRef={getInputsRef}
          format={format}
          localizedFormat={localizedFormat}
          value={momentDates}
          lastDate={lastDate}
          onChange={momentDates => {
            !inputSettings.isLastRange && setMomentDate(momentDates)
            onChangeInput && onChangeInput(momentDates)
          }}
          onChangeInputSettings={onChangeInputSettings}
          onChangeLastFilterSettings={onChangeLastFilterSettings}
          onPressEnter={value => onPressEnter && onPressEnter(value)}
          inputSettings={inputSettings}
          lastFilterSettings={lastFilterSettings}
          placeholder={placeholder}
          open={open}
          showTime={showTime}
          disabled={otherProps.disabled}
          isRange={isRange}
          minDaysRange={minDaysRange}
          isFilter={isFilter}
          onFocus={onFocusedInput}
          disabledDate={disabledDate}
        />
      </div>
      <div
        className={cn('date-picker-calendar-box', {
          [`date-picker-calendar-${activeTab}`]: showTime,
          [`date-picker-calendar-${activeMode}`]: activeMode,
          'date-picker-calendar-witn-time': showTime,
          'date-picker-calendar-month-or-year':
            activeMode !== 'date' && activeTab !== 'time',
        })}
      >
        {showTime && (
          <div className="date-picker-calendar-tab">
            <Tabs
              defaultActiveKey={defaultTab || 'date'}
              onChange={key => {
                setActiveTab(key)
              }}
            >
              <TabPane tab={l('Date')} key="date" />
              <TabPane tab={l('Time')} key="time" />
            </Tabs>
          </div>
        )}
        {showTimeZoneToggle && stringTimeZone !== '0' && (
          <div className="date-picker-calendar-timeZone">
            <Typography
              className="date-picker-calendar-timeZone-title"
              type="div"
              variant="text"
            >
              <FormattedMessage id="Time zone" />:
            </Typography>
            <Radio.Group
              onChange={e => onChangeTimeZone(e.target.value)}
              value={isUTC}
            >
              <Radio value={false}>
                <FormattedMessage id="GMT" /> {stringTimeZone}
              </Radio>
              <Radio value={true}>
                <FormattedMessage id="UTC" />
              </Radio>
            </Radio.Group>
          </div>
        )}
        {activeTab !== 'time' && mode !== 'time' && (
          <DatePicker
            {...otherProps}
            key={renderKey}
            getPopupContainer={container => container}
            format={format}
            dateRender={dateRender}
            value={
              inputSettings.isLastRange
                ? lastDate
                : focusedInput === 'from'
                ? momentDates[0]
                : momentDates[1]
            }
            onChange={onChangeCalendar}
            placeholder={placeholder}
            open={true}
            showTime={false}
            keyboard={false}
            onPanelChange={(_, mode) => setActiveMode(mode)}
            disabledDate={disabledDate}
          />
        )}
        {(activeTab === 'time' || mode === 'time') && (
          <TimePicker
            {...otherProps}
            getPopupContainer={container => container}
            format={TIME_FORMAT_WITH_SECONDS}
            value={
              inputSettings.isLastRange
                ? lastDate
                : focusedInput === 'from'
                ? momentDates[0]
                : momentDates[1]
            }
            onSelect={onChangeCalendar}
            open={true}
            keyboard={false}
          />
        )}
      </div>
    </div>
  )
}

export default CalendarView

CalendarView.propTypes = {
  className: PropTypes.string,
  dateRender: PropTypes.func,
  value: PropTypes.array,
  open: PropTypes.bool,
  format: PropTypes.string,
  localizedFormat: PropTypes.string,
  disabledDate: PropTypes.func,
  onChange: PropTypes.func,
  onPressEnter: PropTypes.func,
  getInputRef: PropTypes.func,
}

CalendarView.defaultProps = {
  value: [null, null],
}
