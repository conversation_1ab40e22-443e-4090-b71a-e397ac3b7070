import React, { useState, useEffect, useCallback } from "react"
import PropTypes from "prop-types"
import moment from "moment"
import { Icon, Popover } from "@develop/fe-library"
import { Input } from "antd"
import cn from "classnames"
import { convertMomentToLocalDate } from "utils/dateConverter"

import { TIME_FORMAT_WITH_SECONDS } from "constants/dateTime"

const START_INPUT_YEAR = 1900

const toMainDateFormat = (stringDate) => {
  // eslint-disable-next-line no-useless-escape
  return stringDate ? stringDate.replace(/[\.\/]/g, "-") : ""
}

const CalendarInput = ({
  value,
  onChange,
  onFocus,
  onBlur,
  placeholder,
  onPressEnter,
  format,
  localizedFormat,
  open,
  disabled,
  showTime,
  getInputRef,
  className,
  disabledDate,
}) => {
  const [formatList, setFormatList] = useState([])
  const [stringDate, setStringDate] = useState("")
  const [focused, setFocused] = useState(null)

  useEffect(() => {
    setFormatList([
      toMainDateFormat(format),
      "DD-MM-YYYY" + (showTime ? ` ${TIME_FORMAT_WITH_SECONDS}` : ""),
    ])
  }, [format, showTime])

  useEffect(() => {
    if (open) {
      const string =
        value && value.isValid()
          ? convertMomentToLocalDate(value, showTime)
          : ""
      if (string && string !== stringDate) {
        setStringDate(string)
      }
    } else {
      setStringDate("")
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [value, open])

  const onChangeString = useCallback(
    (e) => {
      const { target } = e

      if (!target || !target.value) {
        onChange(null, null)
        setStringDate("")
        return
      }

      let stringDate = target.value || ""
      stringDate = stringDate.replace(/\u00A0/, " ")
      setStringDate(stringDate)
      for (let i = 0; i < formatList.length; i++) {
        const momentDate = moment(
          toMainDateFormat(stringDate),
          formatList[i],
          true,
        )
        if (
          momentDate.isValid() &&
          +momentDate.format("YYYY") > START_INPUT_YEAR
        ) {
          if (disabledDate && disabledDate(momentDate)) {
            onChange(moment(), null)
            return
          }
          onChange && onChange(momentDate, stringDate)
          return
        }
      }
      onChange(null, null)
    },
    [onChange, formatList, disabledDate],
  )

  const isPopoverVisible = Boolean(
    focused && stringDate && !moment(stringDate, format, true).isValid(),
  )

  return (
    <Popover
      content={localizedFormat}
      placement="topLeft"
      isVisible={isPopoverVisible}
    >
      <div className={cn("date-picker-calendar-input", className)}>
        <Input
          value={stringDate}
          onChange={onChangeString}
          onFocus={(e) => {
            setFocused(true)
            onFocus && onFocus(e)
          }}
          onBlur={(e) => {
            setFocused(false)
            onBlur && onBlur(e)
          }}
          onPressEnter={() => onPressEnter && onPressEnter()}
          ref={(ref) => ref && getInputRef && getInputRef(ref)}
          placeholder={placeholder ? placeholder : localizedFormat}
          disabled={disabled}
        />
        <Icon name="icnCalendar" size="--icon-size-2" />
      </div>
    </Popover>
  )
}

export default CalendarInput

CalendarInput.propTypes = {
  className: PropTypes.string,
  value: PropTypes.object,
  onChange: PropTypes.func,
  onChangeMoment: PropTypes.func,
  onPressEnter: PropTypes.func,
  format: PropTypes.string,
  localizedFormat: PropTypes.string,
  getInputRef: PropTypes.func,
}

CalendarInput.defaultProps = {
  value: null,
}
