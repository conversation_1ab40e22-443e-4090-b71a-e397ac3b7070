import React from "react"
import withSizes from "react-sizes"

import { getBreakpoint, XS } from "utils/breakpoints"

import { DEFAULT_PAGE, DEFAULT_TOTAL_COUNT } from "constants/grid"

import { useGridFooter } from "./hooks"
import { MobileGridFooter, BasicGridFooter } from "./components"

const GridFooterView = ({
  footerStyles,
  footerClassName,
  footerGridContainerStyles,
  breakpoint,
  width,
  totalResults = DEFAULT_TOTAL_COUNT,
  currentPage = DEFAULT_PAGE,
  tableSettingsVisible = true,
  tableKey,
  displaySettings,
  onPageChange,
  onPageSizeChange,
  searchOptions,
  isSettingButtonsVisible,
  isPageSizeSelectVisible,
  isCopyrightVisibleWithNoData,
  pageSize: pageSizeProp,
  isCustomPageSize = false,
  gridColumns,
  displayBlockedTableColumns = [],
}) => {
  const {
    isResizingMode,
    footerRef,
    pageSize = 25,
    toggleResizingMode<PERSON>andler,
    pageSizeChangeHandler,
  } = useGridFooter({ tableKey, onPageSizeChange })

  const defaultPageSize = +(isCustomPageSize ? pageSizeProp : pageSize)

  return breakpoint === XS ? (
    <MobileGridFooter
      width={width}
      currentPage={currentPage}
      pageSize={defaultPageSize}
      totalResults={totalResults}
      tableSettingsVisible={tableSettingsVisible}
      isResizingMode={isResizingMode}
      footerStyles={footerStyles}
      footerClassName={footerClassName}
      displaySettings={displaySettings}
      onPageChange={onPageChange}
      onPageSizeChange={pageSizeChangeHandler}
      toggleResizingMode={toggleResizingModeHandler}
      footerRef={footerRef}
      searchOptions={searchOptions}
      isSettingButtonsVisible={isSettingButtonsVisible}
      isPageSizeSelectVisible={isPageSizeSelectVisible}
      displayBlockedTableColumns={displayBlockedTableColumns}
    />
  ) : (
    <BasicGridFooter
      width={width}
      currentPage={currentPage}
      pageSize={defaultPageSize}
      totalResults={totalResults}
      tableSettingsVisible={tableSettingsVisible}
      isResizingMode={isResizingMode}
      footerStyles={footerStyles}
      footerClassName={footerClassName}
      footerGridContainerStyles={footerGridContainerStyles}
      displaySettings={displaySettings}
      onPageChange={onPageChange}
      onPageSizeChange={pageSizeChangeHandler}
      toggleResizingMode={toggleResizingModeHandler}
      footerRef={footerRef}
      breakpoint={breakpoint}
      searchOptions={searchOptions}
      isSettingButtonsVisible={isSettingButtonsVisible}
      isPageSizeSelectVisible={isPageSizeSelectVisible}
      isCopyrightVisibleWithNoData={isCopyrightVisibleWithNoData}
      gridColumns={gridColumns}
      displayBlockedTableColumns={displayBlockedTableColumns}
    />
  )
}

const mapSizesToProps = ({ width }) => ({
  breakpoint: getBreakpoint(width),
  width,
})

export const GridFooter = withSizes(mapSizesToProps)(GridFooterView)
