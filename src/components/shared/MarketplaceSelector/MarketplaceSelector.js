import React, { useEffect, useState } from 'react'
import PropTypes from "prop-types"
import cn from 'classnames'
import { Flag, Checkbox } from "@develop/fe-library"

import { countryCode } from 'utils/countryCode'
import l from "utils/intl"

import styles from "./marketplaceSelector.module.scss"

export const MarketplaceSelector = ({
  marketplaces,
  selectedMarketplaces,
  onChange,
  title,
  selectAll,
  className,
  groupKey,
}) => {
  const [isChecked, setChecked] = useState(false)
  const [isDisabled, setDisabled] = useState(false)
  const [selected, setSelected] = useState([])

  useEffect(() => {
    if (selectAll === null) return
    setSelected(selectAll ? marketplaces : [])
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [selectAll])

  useEffect(() => {
    if (!selected.length) setSelected(selectedMarketplaces || [])
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [selectedMarketplaces])

  useEffect(() => {
    if (groupKey) {
      setChecked(!!selected.length)
    }

    setDisabled(!selected.length)

    onChange(groupKey || title, selected)
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [selected])

  const onCheckboxChange = (isChecked) => {
    setChecked(isChecked)
    setDisabled(!isChecked)
    setSelected(marketplaces)

    if (!isChecked) {
      setSelected([])
    }
  }

  const onSelect = (marketplace) => {
    const index = selected.findIndex((m) => m.id === marketplace.id)

    if (index !== -1) {
      setSelected(selected.filter((s) => s.id !== marketplace.id))

      return
    }

    setSelected(selected.concat(marketplace))
  }

  return (
    <div className={cn(styles.marketplaceGroup, className)} key={groupKey}>
      <div className={styles.groupHeader}>
        <Checkbox
          label={l(title)}
          checked={isChecked}
          onChange={onCheckboxChange}
          labelVariant='--font-body-text-2'
        />
      </div>
      <ul className={styles.marketplacesContainer}>
        {marketplaces.map((marketplace) => {
          return (
            <li
              key={marketplace.country}
              className={cn(styles.marketplaceItem, {
                [styles.disabled]: isDisabled,
                [styles.selected]:
                  selected.findIndex((s) => s.id === marketplace.id) !== -1,
              })}
              onClick={() => onSelect(marketplace)}
            >
              <Flag
                className={styles.flagIcon}
                locale={countryCode(marketplace.country)}
                borderRadius="--border-radius-circle"
                size={24}
              />
              {marketplace.country}
            </li>
          )
        })}
      </ul>
    </div>
  )
}

MarketplaceSelector.propTypes = {
  marketplaces: PropTypes.arrayOf(PropTypes.object),
  selectedMarketplaces: PropTypes.arrayOf(PropTypes.object),
  onChange: PropTypes.func,
  title: PropTypes.string,
  selectAll: PropTypes.bool,
  className: PropTypes.string,
  groupKey: PropTypes.string,
}
