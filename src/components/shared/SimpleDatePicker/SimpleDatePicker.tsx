import React, { useState, useCallback } from "react"
import moment from "moment"
import { DatePicker } from "antd"
import { Box } from "@develop/fe-library"
import { useSelector } from "react-redux"

import withOutlineLabel from "components/hocs/withOutlineLabel"
import {WithE<PERSON>rField} from "components/hocs/withErrorField/withErrorField"

import { getDateFormat } from "utils/dateConverter"
import { getDatePickerLocale } from "utils/locales"

import { translationsSelector } from "selectors/mainStateSelectors"

import { SimpleDatePickerProps } from "./SimpleDatePickerTypes"

import styles from "./simpleDatePicker.module.scss"

const DatePickerWithLabel = WithErrorField(withOutlineLabel(DatePicker), true, false)

export const SimpleDatePicker = ({
  disabledDates = [null, null],
  disabledCompareUnitScale = "days",
  value = null,
  onChange = () => {},
  containerProps = {},
  errorText = null,
  ...calendarProps
}: SimpleDatePickerProps) => {
  const [currentValue, setCurrentValue] = useState(value)

  const { locale: language } = useSelector(translationsSelector)
  const datePickerLocale = getDatePickerLocale(language)

  const handleDateSelect = (date: moment.Moment): void => {
    const selectedDate = date || undefined

    setCurrentValue(selectedDate)
    onChange(selectedDate)
  }

  const handleDateDisabled = useCallback(
    (currentDate: moment.Moment): boolean => {
      const [from, to] = disabledDates
      const isRangeDisable = from && to
      const isFromDisable = from && !to
      const isToDisable = !from && to

      if (isRangeDisable) {
        return !currentDate.isBetween(from, to, disabledCompareUnitScale, "[]")
      }

      if (isFromDisable) {
        return currentDate.isBefore(from, disabledCompareUnitScale)
      }

      if (isToDisable) {
        return currentDate.isAfter(from, disabledCompareUnitScale)
      }

      return false
    },
    [disabledCompareUnitScale, disabledDates],
  )

  return (
    <Box className={styles.datePickerContainer} {...containerProps}>
      <DatePickerWithLabel
        {...calendarProps}
        getPopupContainer={(trigger: React.ReactNode) => trigger}
        outlineClassName="date-picker-body" // Reset default Antd styles to design system
        onChange={handleDateSelect}
        value={currentValue}
        format={getDateFormat()}
        locale={datePickerLocale}
        disabledDate={handleDateDisabled}
        errorText={errorText}
      />
    </Box>
  )
}
