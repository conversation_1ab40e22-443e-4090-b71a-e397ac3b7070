.datePickerContainer {
  :global(.ant-picker) {
    border: var(--border-main);
    width: 100%;
  }

  :global(.with-error-field-wraper) :global(.ant-picker) {
    border: var(--border-error);
  }

  :global(.ant-picker-cell:not(.ant-picker-cell-in-view)) {
    cursor: default;
    pointer-events: none;
  }

  :global(.ant-picker-focused) {
    border-color: var(--color-border-active);
  }

  :global(.ant-picker-input) {
    display: inline-flex;
  }

  :global(.with-error-field-icon) {
    background-color: var(--color-main-background);
    padding: 3px;
  }

  :global(.ant-picker-dropdown) {
    padding-top: var(--padding-m);
    background: var(--color-main-background);
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
  }
}
