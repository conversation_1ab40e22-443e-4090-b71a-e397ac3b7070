import React, { useRef, useState } from "react"
import PropTypes from "prop-types"

import { UploadModalWithInputModal } from "./UploadModalWithInputModal"

import { permissionKeys, restrictPopoverMessages } from "constants/permissions"

export const UploadModalWithInput = ({ accept, onUpload, ...props }) => {
  const [selectedFile, setSelectedFile] = useState(undefined)

  const fileInputRef = useRef(null)
  const modalFileName = selectedFile && selectedFile.name

  const handleFileDialogOpen = () => fileInputRef?.current?.click()

  const handleInputChange = ({ target: { files } }) => {
    const file = files[0]

    setSelectedFile(file)
  }

  const handleUpload = () => {
    onUpload(selectedFile)
  }

  return (
    <>
      <UploadModalWithInputModal
        {...props}
        fileName={modalFileName}
        onOpenFileDialog={handleFileDialogOpen}
        onUpload={handleUpload}
        managePermission={permissionKeys.basProductCostImportCreate}
        popoverMessage={restrictPopoverMessages.import}
      />
      <input
        accept={accept}
        name="file"
        onChange={handleInputChange}
        ref={fileInputRef}
        style={{ display: "none" }}
        type="file"
      />
    </>
  )
}

UploadModalWithInput.propTypes = {
  accept: PropTypes.func.isRequired,
  onUpload: PropTypes.func.isRequired,
}
