import { ReactNode } from "react"

import { PopoverMessageType } from "components/hocs/withRestrict/withRestrictTypes"

import { ItemType } from "@develop/fe-library/dist/lib/components/Menu/MenuTypes"

export type GenericWidgetHeaderProps = {
  content: ReactNode
  options?: ItemType[]
  hasFullscreen?: boolean
  onSelectOption?: (index: number) => void
  onFullscreen?: () => void
  managePermission?: boolean
  restrictedPopoverMessage?: PopoverMessageType
}
