import {
  TableSettingsColumnTitles,
  TableSettingsColumnVisibility,
} from "types/TableSettings"

export const ORDER_ITEMS_TABLE_NEW_SETTINGS_KEY =
  "orderItemsTableNewSettings" as const

export const columnTitles: TableSettingsColumnTitles = {
  order_id: "Order ID",
  order_status: "Order Status",
  order_purchase_date: "Purchase Date",
  seller_id: "Amazon Account",
  marketplace_id: "Marketplace",
  product_asin: "ASIN",
  seller_sku: "SKU",
  product_title: "Product Title",
  product_brand: "Brand",
  product_type: "Product Type",
  product_manufacturer: "Manufacturer",
  product_condition: "Condition",
  product_stock_type: "Stock Type",
  offer_type: "Offer Type",
  item_price: "Item Price",
  quantity: "Quantity",
  revenue_amount: "Revenue",
  promotion_amount: "Promotion",
  amazon_fees_amount: "Amazon Fees",
  total_expenses: "Total Expenses",
  total_income: "Total Income",
  estimated_profit_amount: "Estimated Profit",
  quantity_refunded: "Quantity Refunded",
} as const

export const columnDefaultVisibility: TableSettingsColumnVisibility = {
  order_id: true,
  order_status: true,
  order_purchase_date: true,
  seller_id: false,
  marketplace_id: true,
  product_asin: true,
  seller_sku: true,
  product_title: true,
  product_brand: true,
  product_type: true,
  product_manufacturer: true,
  product_condition: true,
  product_stock_type: true,
  offer_type: true,
  item_price: true,
  quantity: true,
  revenue_amount: true,
  promotion_amount: true,
  amazon_fees_amount: true,
  total_expenses: true,
  total_income: true,
  estimated_profit_amount: true,
  quantity_refunded: true,
} as const
