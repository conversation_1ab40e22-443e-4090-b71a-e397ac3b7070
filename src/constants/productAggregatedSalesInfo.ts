import {
  TableSettingsColumnTitles,
  TableSettingsColumnVisibility,
} from "types/TableSettings"

export const PRODUCTS_AGGREGATED_SALES_INFO_TABLE_SETTINGS_KEY =
  "productsSalesInfoTableSettings" as const

export const columnTitles: TableSettingsColumnTitles = {
  image: "Image",
  product_title: "Title",
  product_asin: "ASIN",
  seller_sku: "SKU",
  marketplace_id: "Marketplace",
  product_brand: "Brand",
  product_type: "Product Type",
  product_manufacturer: "Manufacturer",
  units: "Units",
  orders: "Order items",
  refunds: "Refunds",
  revenue_amount: "Revenue",
  estimated_profit_amount: "Estimated margin",
  total_income: "Total income",
  expenses_amount: "Total expenses",
  ppc_costs: "Ads (PPC)",
  amazon_fees: "Amazon fees",
  margin: "Estimated margin %",
  roi: "ROI",
  markup: "Markup",
  seller_id: "Amazon account name",
} as const

export const columnDefaultVisibility: TableSettingsColumnVisibility = {
  image: true,
  product_title: true,
  product_asin: true,
  seller_sku: true,
  marketplace_id: true,
  product_brand: true,
  product_type: true,
  product_manufacturer: true,
  units: true,
  orders: true,
  refunds: true,
  revenue_amount: true,
  estimated_profit_amount: true,
  total_income: true,
  expenses_amount: true,
  ppc_costs: true,
  amazon_fees: true,
  margin: true,
  roi: true,
  markup: true,
  seller_id: false,
} as const
