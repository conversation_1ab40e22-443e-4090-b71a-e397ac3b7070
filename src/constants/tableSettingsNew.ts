import { ColumnSetting } from "@develop/fe-library"
import { getObjectEntries, getObjectKeys } from "@develop/fe-library/dist/utils"

import {
  columnDefaultVisibility,
  columnTitles,
  PRODUCTS_AGGREGATED_SALES_INFO_TABLE_SETTINGS_KEY,
} from "constants/productAggregatedSalesInfo"

import { TableSettingsMap } from "types/TableSettings"

export const PAGE_DEFAULT = 1
export const PAGE_SIZE_DEFAULT = 25
export const SORT_DEFAULT = "-id"

export const tableSettings: TableSettingsMap = {
  [PRODUCTS_AGGREGATED_SALES_INFO_TABLE_SETTINGS_KEY]: {
    titles: columnTitles,
    defaultVisibility: columnDefaultVisibility,
  },
} as const

export const defaultSettings: Record<
  string,
  Array<ColumnSetting>
> = getObjectKeys(tableSettings).reduce((acc, key) => {
  const tableSetting = tableSettings[key]

  if (!tableSetting) {
    return acc
  }

  const { titles, defaultVisibility } = tableSetting

  return {
    ...acc,
    [key]: getObjectEntries(titles).map(([name, label]) => ({
      label,
      name,
      value: defaultVisibility[name],
    })),
  }
}, {})
