import { SellerMarketplaceParams } from "types/SellerMarketplaceParams"
import {
  MarketplaceIdUrlParams,
  ProductFiltersUrlParams,
  SellerIdUrlParams,
} from "types/UrlParams"

export type GetSellerMarketplaceParams = SellerIdUrlParams &
  MarketplaceIdUrlParams &
  ProductFiltersUrlParams

export type UseSellerMarketplaceParamsReturnType = {
  getSellerMarketplaceParams: (
    urlParams: GetSellerMarketplaceParams,
  ) => SellerMarketplaceParams
}
