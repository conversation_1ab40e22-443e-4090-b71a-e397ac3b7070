import { useCallback } from "react"
import { useDispatch } from "react-redux"

import userSettingsActions from "actions/userSettingsActions"

import {
  CreateUserSettingsParams,
  GetUserSettingsParams,
  UpdateUserSettingsParams,
} from "./useUserSettingsCommonTypes"

export const useUserSettingsCommon = () => {
  const dispatch = useDispatch()

  const getUserSettings = useCallback(
    ({
      key,
      successCallback,
      failureCallback,
    }: GetUserSettingsParams): void => {
      dispatch(
        userSettingsActions.getUserSettings(
          { key },
          successCallback || null,
          failureCallback || null,
        ),
      )
    },
    [],
  )

  const createUserSettings = useCallback(
    <Value>({
      key,
      value,
      successCallback,
      failureCallback,
    }: CreateUserSettingsParams<Value>): void => {
      dispatch(
        userSettingsActions.saveUserSettings(
          {
            key,
            settings: value,
          },
          successCallback || null,
          failureCallback || null,
        ),
      )
    },
    [],
  )

  const updateUserSettings = useCallback(
    <Value>({
      id,
      value,
      successCallback,
      failureCallback,
    }: UpdateUserSettingsParams<Value>) => {
      dispatch(
        userSettingsActions.updateUserSettings(
          {
            id,
            settings: value,
          },
          successCallback || null,
          failureCallback || null,
        ),
      )
    },
    [],
  )

  const deleteUserSettings = useCallback((id: number) => {
    dispatch(userSettingsActions.deleteUserSettings(id))
  }, [])

  return {
    getUserSettings,
    createUserSettings,
    updateUserSettings,
    deleteUserSettings,
  }
}
