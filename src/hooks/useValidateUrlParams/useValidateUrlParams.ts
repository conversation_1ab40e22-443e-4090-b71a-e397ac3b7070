import { useCallback } from "react"
import { format, isValid } from "date-fns"
import { InputMode } from "@develop/fe-library"

import { useGroupAccountOptions, useMinStatsDate } from "hooks"

import { getDateRangeInterval, validateInputMode } from "utils/dateRange"

import { DEFAULT_CURRENCY_CODE } from "constants/currencies"
import { DATE_FNS_FORMATS } from "constants/dateTime"
import { OFFER_TYPE_VALUES } from "constants/offerType"
import { PAGE_VIEW, PAGE_VIEW_VALUES } from "constants/pageView"

import { DashboardFiltersParams, SellerIdUrlParams } from "types"

/**
 * Description:
 * 1. Validate ONLY new `seller_id` is present in `groupAccountOptions`
 * 2. Validate Dashboard common params
 */
export const useValidateUrlParams = () => {
  const { options: groupAccountOptions } = useGroupAccountOptions()
  const { minStatsDate: fromDate, today: toDate } = useMinStatsDate()

  /**
   * Description:
   * This can ONLY validate new `seller_id`,
   * NOT the old one where `sellerId` can become `groupId`
   */
  const validateUrlParamsSellerId = useCallback(
    ({ seller_id }: SellerIdUrlParams = {}): string | undefined => {
      if (!seller_id) {
        return undefined
      }

      // WARN: groupAccountOptions should be loaded
      const isSellerIdValid = groupAccountOptions.some(
        (option) => option.value === seller_id,
      )

      return isSellerIdValid ? seller_id : undefined
    },
    [groupAccountOptions],
  )

  const getDefaultParams = useCallback(
    (
      params: DashboardFiltersParams,
      inputMode: InputMode,
    ): DashboardFiltersParams => ({
      ...params,
      seller_id: validateUrlParamsSellerId(params),
      inputMode,
      currency_code: params.currency_code || DEFAULT_CURRENCY_CODE,
      offer_type:
        params.offer_type && OFFER_TYPE_VALUES.includes(params.offer_type)
          ? params.offer_type
          : undefined,
      view:
        params.view && PAGE_VIEW_VALUES.includes(params.view)
          ? params.view
          : PAGE_VIEW.ORDER,
    }),
    [validateUrlParamsSellerId],
  )

  /**
   * Description:
   * 1. Validate Dashboard common params
   * 2. Merge provided params with default/corrected values
   */
  const validateDashboardUrlParams = useCallback(
    (params: DashboardFiltersParams): DashboardFiltersParams => {
      const inputMode = validateInputMode(params.inputMode as InputMode)

      // Validate dates before creating Date objects
      const fromDateObj = params.from ? new Date(params.from) : undefined
      const toDateObj = params.to ? new Date(params.to) : undefined

      if (fromDateObj && !isValid(fromDateObj)) {
        // Invalid from date: params.from
        return getDefaultParams(params, inputMode)
      }

      if (toDateObj && !isValid(toDateObj)) {
        // Invalid to date: params.to
        return getDefaultParams(params, inputMode)
      }

      try {
        const interval = getDateRangeInterval({
          from: fromDateObj,
          to: toDateObj,
          inputMode,
          fromDate,
          toDate,
        })

        return {
          ...getDefaultParams(params, inputMode),
          from: format(interval.start, DATE_FNS_FORMATS.SERVER),
          to: format(interval.end, DATE_FNS_FORMATS.SERVER),
        }
      } catch (error) {
        return getDefaultParams(params, inputMode)
      }
    },
    [fromDate, toDate, getDefaultParams],
  )

  return {
    validateUrlParamsSellerId,
    validateDashboardUrlParams,
  }
}
