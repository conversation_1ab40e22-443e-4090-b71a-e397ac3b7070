export const operationalDashboardState = {
  dataCompleteness: [],
  isDataCompletenessLoading: false,
  isVisibleDataReassemblyModal: false,
  isVisibleHistoricalDataLoadModal: false,
  isVisibleReferralFeeChangesDrawer: false,
  isVisibleFbaFulfillmentFeeChangesDrawer: false,
  isVisibleAdjustmentToFeesDrawer: false,
  additionalDataModal: {},
  dataReferralFeeChanges: [],
  isDataReferralFeeChangesLoading: false,
  totalCount: 0,
  dataFbaFulfillmentFeeChanges: [],
  isDataFbaFulfillmentFeeChangesLoading: false,
  dataAdjustmentToFees: [],
  isDataAdjustmentToFeesLoading: false,
  fillPercentageWidgetHealthScoreLevel: 0,
}
