 const cacheAPIMiddleware = ({ getState, dispatch }) => {
  return next => action => {
    const {
      type,
      payload: {
        cache,
        cacheKey,
        cacheTimeout = 600000,
        callback,
        successCallback,
      } = {},
    } = action

    if (!Array.isArray(type)) {
      // Normal action: pass it on
      return next(action)
    }

    if (!cache) {
      // should not be cached: pass it on
      return next(action)
    }

    if (type.length !== 3 || !type.every(type => typeof type === 'string')) {
      throw new Error('Expected an array of three string types.')
    }

    if (typeof cache !== 'boolean') {
      throw new Error('Expected cache to be a boolean value')
    }

    if (!cacheKey || typeof cacheKey !== 'string') {
      throw new Error('Expected cacheKey to be a string value')
    }

    const [, successType] = type
    const {
      cacheAPI: {
        cache: { [cacheKey]: { payload, timestamp } = {} },
      },
    } = getState()

    if (!payload || Date.now() - timestamp > cacheTimeout) {
      // cache expired or not exists: pass it on
      return next(action)
    }

    dispatch({
      payload,
      type: successType,
    })

    successCallback && successCallback(payload)
    callback && callback()
  }
}

export default cacheAPIMiddleware
