import getMainApp from "components/MainAppProvider"
import { checkIsFunction } from "utils/validationHelper"

let reloading = false

const setReloading = (value) => {
  reloading = value
}

const callAPIMiddleware = ({ dispatch }) => {
  return (next) => (action) => {
    const { utils: mainAppUtils } = getMainApp()

    const { buildCallAPIMiddlewareLogic } = mainAppUtils || {}

    const isReady = checkIsFunction(buildCallAPIMiddlewareLogic)

    if (!isReady) {
      return next(action)
    }

    return buildCallAPIMiddlewareLogic({
      dispatch,
      next,
      action,
      reloading,
      setReloading,
    })
  }
}

export default callAPIMiddleware
