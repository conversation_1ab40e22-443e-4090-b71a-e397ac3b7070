import { types as ActionTypes } from "actions/dashboard"

import generateReducer from "utils/generateReducer"

import { dashboardInitialState } from "./initialState"

import { DashboardState } from "./dashboardReducersTypes"
import { Reduction } from "reducers/types"

const { setDefaultValuesStatus } = ActionTypes

const reductions: Reduction<DashboardState> = {
  [setDefaultValuesStatus]: (state, payload) => {
    return {
      ...state,
      defaultValuesStatus: payload,
    }
  },
}

const reducer = generateReducer(dashboardInitialState, reductions)

export default reducer
