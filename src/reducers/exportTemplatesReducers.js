import { exportTemplatesInitialState } from "initialState/exportTemplates"

import { types as ActionTypes } from "actions/exportTemplatesActions"

import generateReducer from "utils/generateReducer"

const reductions = {
  [ActionTypes.setIsExportTemplateDrawerVisible]: (
    state,
    { isVisible, templateId = null },
  ) => ({
    ...state,
    isExportTemplateDrawerVisible: isVisible,
    editTemplateId: isVisible ? templateId : null,
  }),
  [ActionTypes.getExportTemplates[0]]: (state) => ({
    ...state,
    isExportTemplateLoading: true,
  }),
  [ActionTypes.getExportTemplates[1]]: (
    state,
    { currentPage, data, pageSize, totalCount },
  ) => {
    return {
      ...state,
      exportTemplates: data,
      totalCount,
      searchOptions: {
        ...state.searchOptions,
        page: currentPage,
        pageSize: pageSize,
      },
      isExportTemplateLoading: false,
    }
  },
  [ActionTypes.getExportTemplates[2]]: (state) => ({
    ...state,
    isExportTemplateLoading: false,
  }),
  [ActionTypes.resetCurrentTemplate]: (state) => ({
    ...state,
    currentTemplate: {},
    isTemplateLoaded: false,
    isExportTemplateLoading: false,
    isExportTemplateFiltersFieldsLoading: false,
    isExportTemplateFieldGroupLoading: false,
  }),
  [ActionTypes.changeSearchOptions]: (state, searchOptions) => ({
    ...state,
    searchOptions,
  }),
  [ActionTypes.getAllExportTemplates[1]]: (state, data) => {
    return {
      ...state,
      allExportTemplates: data,
    }
  },
  [ActionTypes.getExportTemplateById[0]]: (state) => ({
    ...state,
    isTemplateLoaded: false,
    isExportTemplateLoading: true,
  }),
  [ActionTypes.getExportTemplateById[1]]: (state, payload) => ({
    ...state,
    currentTemplate: { ...state.currentTemplate, ...payload },
    isTemplateLoaded: true,
    isExportTemplateLoading: false,
  }),
  [ActionTypes.getExportTemplateOrderFiltersFields[0]]: (state) => ({
    ...state,
    isExportTemplateOrderFiltersFieldsLoading: true,
  }),
  [ActionTypes.getExportTemplateOrderFiltersFields[1]]: (state, payload) => ({
    ...state,
    currentTemplate: { ...state.currentTemplate, orderFilterFields: payload },
    isExportTemplateOrderFiltersFieldsLoading: false,
  }),
  [ActionTypes.getExportTemplateFiltersFields[0]]: (state) => ({
    ...state,
    isExportTemplateFiltersFieldsLoading: true,
  }),
  [ActionTypes.getExportTemplateFiltersFields[1]]: (state, payload) => ({
    ...state,
    currentTemplate: { ...state.currentTemplate, filterFields: payload },
    isExportTemplateFiltersFieldsLoading: false,
  }),
  [ActionTypes.getExportTemplateFieldGroup[0]]: (state) => ({
    ...state,
    isExportTemplateFieldGroupLoading: true,
  }),
  [ActionTypes.getExportTemplateFieldGroup[1]]: (state, payload) => ({
    ...state,
    currentTemplate: { ...state.currentTemplate, fieldGroup: payload },
    isExportTemplateFieldGroupLoading: false,
  }),
}

export default generateReducer(exportTemplatesInitialState, reductions)
