import { types as ActionTypes } from "actions/kpiWidgetsActions"

import generateReducer from "utils/generateReducer"

import { KPIWidgetsState } from "./kpiWidgetsReducersTypes"
import { Reduction } from "reducers/types"

const { setDateRangeByIndex, setKPIWidgetsSettings, setIsInitiated } =
  ActionTypes

const initialState: KPIWidgetsState = {
  isInitiated: false,
  kpiWidgetsSettings: [],
}

const reductions: Reduction<KPIWidgetsState> = {
  [setIsInitiated]: (state, payload) => {
    return {
      ...state,
      isInitiated: payload,
    }
  },
  [setDateRangeByIndex]: (state, { index, settings } = {}) => {
    return {
      ...state,
      kpiWidgetsSettings: state.kpiWidgetsSettings.map(
        (kpiWidgetsSetting, i) => {
          if (i === index) {
            return {
              ...kpiWidgetsSetting,
              inputMode: settings.inputMode,
              selected: settings.selected,
            }
          }

          return kpiWidgetsSetting
        },
      ),
    }
  },
  [setKPIWidgetsSettings]: (state, payload) => {
    return {
      ...state,
      kpiWidgetsSettings: payload,
    }
  },
}

const reducer = generateReducer(initialState, reductions)

export default reducer
