import { OrderItemsTableInitialData } from "initialState/orderItemsTable"

import { ASYNC_STATUSES } from "constants/async"
import { ORDER_ITEMS_TABLE_NEW_SETTINGS_KEY } from "constants/orderItemsTableNew"

import type { OrderItemsTableNewState } from "./orderItemsTableNewReducersTypes"

export const orderItemsTableNewInitialState: OrderItemsTableNewState = {
  orderItems: {
    data: OrderItemsTableInitialData,
    status: ASYNC_STATUSES.IDLE,
    error: null,
  },
  tableSettings: {
    id: null,
    key: ORDER_ITEMS_TABLE_NEW_SETTINGS_KEY,
    pageSize: 25,
    settings: [],
  },
  tableSettingsStatus: ASYNC_STATUSES.PENDING,
}
