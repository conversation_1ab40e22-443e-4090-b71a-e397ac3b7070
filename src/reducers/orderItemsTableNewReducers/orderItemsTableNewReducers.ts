import { API_STATUS } from "api/constants"

import {
  orderItemsTableNewActionsPrefix,
  types as ActionTypes,
} from "actions/orderItemsTableNewActions"

import type { Reduction } from "reducers/types"

import generateReducer from "utils/generateReducer"
import { buildTableSettingsReductions } from "utils/tableSettingsHelpers"

import { ASYNC_STATUSES } from "constants/async"

import { orderItemsTableNewInitialState } from "./initialState"

import type { GridGeneral, OrderStatuses } from "types"
import { AmazonOrderExtendedViewItemV1 } from "types/Models/AmazonOrderExtendedViewItem"
import type { OrderItemsTableNewState } from "./orderItemsTableNewReducersTypes"

const {
  getAmazonOrders,
  getAmazonOrderStatuses,
  clear,
  getTableSettings,
  updateTableSettings,
  updateDefaultTableSettings,
} = ActionTypes

const { REQUEST, SUCCESS, FAILURE } = API_STATUS

const reductions: Reduction<OrderItemsTableNewState> = {
  [getAmazonOrders[REQUEST]]: (state): OrderItemsTableNewState => {
    return {
      ...state,
      orderItems: {
        ...state.orderItems,
        status: ASYNC_STATUSES.PENDING,
      },
    }
  },
  [getAmazonOrders[SUCCESS]]: (
    state,
    payload: GridGeneral<AmazonOrderExtendedViewItemV1[]>,
  ): OrderItemsTableNewState => {
    return {
      ...state,
      orderItems: {
        error: null,
        status: ASYNC_STATUSES.FULFILLED,
        data: payload,
      },
    }
  },
  [getAmazonOrders[FAILURE]]: (state, payload): OrderItemsTableNewState => {
    return {
      ...state,
      orderItems: {
        ...state.orderItems,
        status: ASYNC_STATUSES.REJECTED,
        error: payload,
      },
    }
  },
  [getAmazonOrderStatuses[REQUEST]]: (state): OrderItemsTableNewState => {
    return {
      ...state,
      orderStatuses: {
        ...state.orderStatuses,
        status: ASYNC_STATUSES.PENDING,
      },
    }
  },
  [getAmazonOrderStatuses[SUCCESS]]: (
    state,
    payload: OrderStatuses,
  ): OrderItemsTableNewState => {
    return {
      ...state,
      orderStatuses: {
        error: null,
        status: ASYNC_STATUSES.FULFILLED,
        data: payload,
      },
    }
  },
  [getAmazonOrderStatuses[FAILURE]]: (state, payload): OrderItemsTableNewState => {
    return {
      ...state,
      orderStatuses: {
        ...state.orderStatuses,
        status: ASYNC_STATUSES.REJECTED,
        error: payload,
      },
    }
  },
  [clear]: (): OrderItemsTableNewState => {
    return orderItemsTableNewInitialState
  },
  ...buildTableSettingsReductions({
    storeKey: orderItemsTableNewActionsPrefix,
  }),
}

export const orderItemsTableNewReducers = generateReducer<OrderItemsTableNewState>(
  reductions,
  orderItemsTableNewInitialState,
)
