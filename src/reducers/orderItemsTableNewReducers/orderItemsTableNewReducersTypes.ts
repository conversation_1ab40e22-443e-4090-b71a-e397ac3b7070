import { AsyncStatus, <PERSON>rid<PERSON>eneral, OrderStatuses } from "types"
import { AmazonOrderExtendedViewItemV1 } from "types/Models/AmazonOrderExtendedViewItem"
import { TableSettingsState } from "types/TableSettings"

export type OrderItemsTableNewState = {
  orderItems: {
    data: GridGeneral<AmazonOrderExtendedViewItemV1[]>
    status: AsyncStatus
    error: any | null
  }
  orderStatuses: {
    data: OrderStatuses | null
    status: AsyncStatus
    error: any | null
  }
} & TableSettingsState
