import { OrderItemsTableInitialData } from "initialState/orderItemsTable"

import { ASYNC_STATUSES } from "constants/async"

import { OrderItemsTableState } from "./orderItemsTableReducersTypes"

export const orderItemsTableInitialState: OrderItemsTableState = {
  searchOptions: {},
  orderItems: {
    data: OrderItemsTableInitialData,
    status: ASYNC_STATUSES.IDLE,
    error: null,
  },
  orderStatuses: {
    data: null,
    status: ASYNC_STATUSES.IDLE,
    error: null,
  },
}
