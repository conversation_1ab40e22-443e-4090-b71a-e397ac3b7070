import { API_STATUS } from "api/constants"

import { types as ActionTypes } from "actions/orderItemsTableActions"

import generateReducer from "utils/generateReducer"

import { ASYNC_STATUSES } from "constants/async"

import { orderItemsTableInitialState } from "./initialState"

import { GridGeneral, OrderStatuses } from "types"
import { AmazonOrderExtendedViewItemV1 } from "types/Models/AmazonOrderExtendedViewItem"

import { OrderItemsTableState } from "./orderItemsTableReducersTypes"
import { Reduction } from "reducers/types"

const {
  changeSearchOptions,
  clearAmazonOrdersData,
  getAmazonOrders,
  getAmazonOrderStatuses,
  clearAmazonOrderStatuses,
} = ActionTypes

const { FAILURE, REQUEST, SUCCESS } = API_STATUS

const reductions: Reduction<OrderItemsTableState> = {
  [getAmazonOrders[REQUEST]]: (state): OrderItemsTableState => {
    return {
      ...state,
      orderItems: {
        ...state.orderItems,
        status: ASYNC_STATUSES.PENDING,
      },
    }
  },
  [getAmazonOrders[SUCCESS]]: (
    state,
    payload: GridGeneral<AmazonOrderExtendedViewItemV1[]>,
  ): OrderItemsTableState => {
    return {
      ...state,
      orderItems: {
        error: null,
        status: ASYNC_STATUSES.FULFILLED,
        data: payload,
      },
    }
  },
  [getAmazonOrders[FAILURE]]: (state, payload): OrderItemsTableState => {
    return {
      ...state,
      orderItems: {
        ...state.orderItems,
        status: ASYNC_STATUSES.REJECTED,
        error: payload,
      },
    }
  },
  [clearAmazonOrdersData]: (state): OrderItemsTableState => {
    return {
      ...state,
      orderItems: { ...orderItemsTableInitialState.orderItems },
    }
  },

  [getAmazonOrderStatuses[REQUEST]]: (state): OrderItemsTableState => {
    return {
      ...state,
      orderStatuses: {
        ...state.orderStatuses,
        status: ASYNC_STATUSES.PENDING,
      },
    }
  },
  [getAmazonOrderStatuses[SUCCESS]]: (
    state,
    payload: OrderStatuses,
  ): OrderItemsTableState => {
    return {
      ...state,
      orderStatuses: {
        ...state.orderStatuses,
        status: ASYNC_STATUSES.FULFILLED,
        data: payload,
      },
    }
  },
  [getAmazonOrderStatuses[FAILURE]]: (state, payload): OrderItemsTableState => {
    return {
      ...state,
      orderStatuses: {
        ...state.orderStatuses,
        status: ASYNC_STATUSES.REJECTED,
        error: payload,
      },
    }
  },
  [clearAmazonOrderStatuses]: (state): OrderItemsTableState => {
    return {
      ...state,
      orderStatuses: { ...orderItemsTableInitialState.orderStatuses },
    }
  },

  [changeSearchOptions]: (state, payload): OrderItemsTableState => {
    return {
      ...state,
      searchOptions: payload,
    }
  },
}

const reducer = generateReducer(orderItemsTableInitialState, reductions)

export default reducer
