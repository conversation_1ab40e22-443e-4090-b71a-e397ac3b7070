import { types as ActionTypes } from "actions/ordersActions"

import generateReducer from "utils/generateReducer"

import { OrdersState, OrdersStateReductions } from "./ordersReducersTypes"

const ordersInitialState: OrdersState = {
  totalCount: 0,
  searchOptions: {}, // Should be empty object as it usage in many places with destructuring
  prevSearchOptions: null,
  urlParams: {}, // Should be empty object as it usage in many places with destructuring
  orderStatuses: null,
  lastUpdateDate: null,
  amazonFeesBreakdown: null,
  expensesBreakdown: null,
  orderDetails: null,
  modalName: null,
  isModalVisible: false,
  currentOrderItemId: null,
  currentOrderId: null,
  isModalDataLoading: false,
  currentOrderCurrencyId: null,
  orderAmountCosts: null,
  isOrdersDataLoading: false,

  data: [],
}

const reductions: OrdersStateReductions = {
  [ActionTypes.displayModal]: (
    state,
    {
      isModalVisible,
      modalName,
      currentOrderItemId,
      currentOrderId,
      currentOrderCurrencyId,
    },
  ) => ({
    ...state,
    modalName: isModalVisible ? modalName : null,
    isModalVisible,
    currentOrderItemId: isModalVisible ? currentOrderItemId : null,
    currentOrderId: isModalVisible ? currentOrderId : null,
    currentOrderCurrencyId,
  }),
  [ActionTypes.updateUrlParams]: (state, urlParams) => ({
    ...state,
    urlParams: {
      ...state.urlParams,
      ...urlParams,
    },
  }),
  [ActionTypes.changeSearchOptions]: (state, searchOptions) => ({
    ...state,
    searchOptions,
    prevSearchOptions: state.searchOptions,
  }),
  [ActionTypes.getOrders[0]]: (state) => ({
    ...state,
    isOrdersDataLoading: true,
  }),
  [ActionTypes.getOrders[1]]: (state, { totalCount, data }) => ({
    ...state,
    data,
    totalCount,
    isOrdersDataLoading: false,
  }),
  [ActionTypes.getOrders[2]]: (state) => ({
    ...state,
    data: [],
    totalCount: 0,
    isOrdersDataLoading: false,
  }),
  [ActionTypes.getOrderStatuses[1]]: (state, orderStatuses) => ({
    ...state,
    orderStatuses,
  }),
  [ActionTypes.getLastUpdateOrdersDate[1]]: (state, { updatedAt }) => ({
    ...state,
    lastUpdateDate: updatedAt,
  }),
  [ActionTypes.getAmazonOrderFeesBreakdown[0]]: (state) => ({
    ...state,
    isModalDataLoading: true,
  }),
  [ActionTypes.getAmazonOrderFeesBreakdown[1]]: (state, payload) => ({
    ...state,
    amazonFeesBreakdown: payload,
    isModalDataLoading: false,
  }),
  [ActionTypes.getAmazonOrderFeesBreakdown[2]]: (state) => ({
    ...state,
    isModalDataLoading: false,
  }),
  [ActionTypes.getAmazonOrderExpensesBreakdown[0]]: (state) => ({
    ...state,
    isModalDataLoading: true,
  }),
  [ActionTypes.getAmazonOrderExpensesBreakdown[1]]: (state, payload) => ({
    ...state,
    expensesBreakdown: payload,
    isModalDataLoading: false,
  }),
  [ActionTypes.getAmazonOrderExpensesBreakdown[2]]: (state) => ({
    ...state,
    isModalDataLoading: false,
  }),
  [ActionTypes.getAmazonOrderDetails[0]]: (state) => ({
    ...state,
    isModalDataLoading: true,
  }),
  [ActionTypes.getAmazonOrderDetails[1]]: (state, payload) => ({
    ...state,
    orderDetails: payload,
    isModalDataLoading: false,
  }),
  [ActionTypes.getAmazonOrderDetails[2]]: (state) => ({
    ...state,
    isModalDataLoading: false,
  }),
  [ActionTypes.getAmazonOrderAmountCosts[1]]: (state, payload) => ({
    ...state,
    orderAmountCosts: payload,
  }),
  [ActionTypes.updateAmazonOrderAmountCosts[1]]: (state, payload) => ({
    ...state,
    orderAmountCosts: payload,
  }),
}

export default generateReducer(ordersInitialState, reductions)
