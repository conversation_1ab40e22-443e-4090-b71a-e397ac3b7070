import { ProductsSalesInfoTableInitialData } from "initialState/productsAggregatedSalesInfoTable"

import { ASYNC_STATUSES } from "constants/async"
import { PRODUCTS_AGGREGATED_SALES_INFO_TABLE_SETTINGS_KEY } from "constants/productAggregatedSalesInfo"

import type { ProductAggregatedSalesInfoState } from "types/store/ProductAggregatedSalesInfo"

export const productsAggregatesSalesInfoInitialState: ProductAggregatedSalesInfoState =
  {
    productAggregatedSalesInfo: {
      data: ProductsSalesInfoTableInitialData,
      status: ASYNC_STATUSES.IDLE,
      error: null,
    },
    tableSettings: {
      id: null,
      key: PRODUCTS_AGGREGATED_SALES_INFO_TABLE_SETTINGS_KEY,
      pageSize: 25,
      settings: [],
    },
    tableSettingsStatus: ASYNC_STATUSES.PENDING,
  }
