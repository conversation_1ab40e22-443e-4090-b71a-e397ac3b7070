import { API_STATUS } from "api/constants"

import {
  productAggregatedSalesInfoActionsPrefix,
  types as ActionTypes,
} from "actions/productAggregatedSalesInfoActions"

import type { Reduction } from "reducers/types"

import generateReducer from "utils/generateReducer"
import { buildTableSettingsReductions } from "utils/tableSettingsHelpers"

import { ASYNC_STATUSES } from "constants/async"

import { productsAggregatesSalesInfoInitialState } from "./initialState"

import type { GridGeneral, ProductAggregatedSalesInfo } from "types"
import type { ProductAggregatedSalesInfoState } from "types/store/ProductAggregatedSalesInfo"

const {
  getProductAggregatedSalesInfo,
  getTableSettings,
  updateTableSettings,
  updateDefaultTableSettings,
} = ActionTypes

const { FAILURE, REQUEST, SUCCESS } = API_STATUS

const reductions: Reduction<ProductAggregatedSalesInfoState> = {
  [getProductAggregatedSalesInfo[REQUEST]]: (
    state,
  ): ProductAggregatedSalesInfoState => {
    return {
      ...state,
      productAggregatedSalesInfo: {
        ...state.productAggregatedSalesInfo,
        status: ASYNC_STATUSES.PENDING,
      },
    }
  },
  [getProductAggregatedSalesInfo[SUCCESS]]: (
    state,
    payload: GridGeneral<ProductAggregatedSalesInfo[]>,
  ): ProductAggregatedSalesInfoState => {
    return {
      ...state,
      productAggregatedSalesInfo: {
        error: null,
        status: ASYNC_STATUSES.FULFILLED,
        data: payload,
      },
    }
  },
  [getProductAggregatedSalesInfo[FAILURE]]: (
    state,
    payload,
  ): ProductAggregatedSalesInfoState => {
    return {
      ...state,
      productAggregatedSalesInfo: {
        ...state.productAggregatedSalesInfo,
        status: ASYNC_STATUSES.REJECTED,
        error: payload,
      },
    }
  },
  ...buildTableSettingsReductions<ProductAggregatedSalesInfoState>(
    productAggregatedSalesInfoActionsPrefix,
  ),
}

const reducer = generateReducer(
  productsAggregatesSalesInfoInitialState,
  reductions,
)

export default reducer
