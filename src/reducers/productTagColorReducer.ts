import { ACTIONS_STATE } from "actions/general"
import { types as ActionTypes } from "actions/productTagColorActions"

import generateReducer from "utils/generateReducer"

const { SUCCESS, FAILURE, REQUEST } = ACTIONS_STATE

const initialState = {
  initialValues: {},
  productTagColors: [],
  searchOptions: {},
  isLoading: true,
}

const reductions = {
  [ActionTypes.getProductTagColors[REQUEST]]: (state) => ({
    ...state,
    isLoading: true,
  }),
  [ActionTypes.getProductTagColors[SUCCESS]]: (state, payload) => ({
    ...state,
    productTagColors: payload,
    isLoading: false,
  }),
  [ActionTypes.getProductTagColors[FAILURE]]: (state) => ({
    ...state,
    isLoading: false,
  }),

  [ActionTypes.createProductTagColor[SUCCESS]]: (state) => ({
    ...state,
    initialValues: {},
  }),
}

export default generateReducer(initialState, reductions)
