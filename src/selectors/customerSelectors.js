import { createSelector } from "reselect"

export const customerStateSelector = (state) => state.mainState.customer

export const isActiveFullServiceSelector = (state) => {
  const {
    allow_lost_full_service,
    use_lost_module,
    lost_module_signed_full_service,
  } = customerStateSelector(state)?.customer || {}

  return Boolean(
    allow_lost_full_service &&
      use_lost_module &&
      !lost_module_signed_full_service,
  )
}

export const isDemoAccountSelector = createSelector(
  customerStateSelector,
  ({ customer }) => {
    if (customer) {
      const { is_demo } = customer

      return !!is_demo
    }
  },
)

export const customerProductsInfoSelector = createSelector(
  customerStateSelector,
  ({ customer }) => {
    const isSelectedForNewPricing =
      !!customer?.selected_for_new_pricing && !customer?.use_new_pricing

    return {
      useNewPricing: !!customer?.use_new_pricing,
      useRepricer: customer?.use_repricer_module,
      useRepricerB2C: customer?.use_repricer_b2c_module,
      useRepricerB2B: customer?.use_repricer_b2b_module,
      useLostModule: customer?.use_lost_module,
      useBasModule: customer?.use_bas_module,
      isBasModuleStarted: !!customer?.bas_module_started,
      isSelectedForNewPricing,
      isUpgradeRepricerSubscription: !!customer?.repricerSubscriptionInfo,
    }
  },
)

export const customerRepricerSubscriptionInfoSelector = createSelector(
  customerStateSelector,
  ({ customer }) => {
    return {
      useNewPricing: !!customer?.use_new_pricing,
      isUpgradeRepricerSubscription: !!customer?.repricerSubscriptionInfo,
      repricerSubscriptionInfo: customer?.repricerSubscriptionInfo,
      isProductImportAllowed: !!customer?.use_new_pricing
        ? !!customer?.repricerSubscriptionInfo?.productImportAllowed
        : true,
      isProductExportAllowed: !!customer?.use_new_pricing
        ? !!customer?.repricerSubscriptionInfo?.productExportAllowed
        : true,
      isProductBulkEditAllowed: !!customer?.use_new_pricing
        ? !!customer?.repricerSubscriptionInfo?.productBulkEditAllowed
        : true,
      isApiAllowed: !!customer?.use_new_pricing
        ? !!customer?.repricerSubscriptionInfo?.apiAllowed
        : true,
      isUserPermissionsAllowed: !!customer?.use_new_pricing
        ? !!customer?.repricerSubscriptionInfo?.userPermissionsAllowed
        : true,
      isRepricerToBASyncAllowed: !!customer?.use_new_pricing
        ? !!customer?.repricerSubscriptionInfo?.repricerToBASyncAllowed
        : true,
    }
  },
)
