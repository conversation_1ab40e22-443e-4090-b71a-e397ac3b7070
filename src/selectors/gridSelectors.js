import { getObjectEntries, getObjectKeys } from "@develop/fe-library/dist/utils"
import { createSelector } from "reselect"

import {
  amazonCustomerAccountsSelector as mainAmazonCustomerAccountsSelector,
  basAmazonCustomerAccountsSelector,
} from "selectors/mainStateSelectors"

import { checkIsArray } from "utils/arrayHelpers"
import { profitTypes, strategiesTypes } from "utils/formConstants"
import l from "utils/intl"

import { FBA_PROGRAM_TYPES, STOCK_TYPES } from "constants/formConstants"

import { productTagsSelector } from "./productTagsSelectors"

const gridStateSelector = (state) => state.grid

export const tableSettingsSelector = (state) => state.tableSettings
const productsForFilterSelector = (state) => state.grid.productsList.data

export const amazonCustomerAccountsSelector = (state) =>
  mainAmazonCustomerAccountsSelector(state).amazonCustomerAccounts || []
export const getGridPrevSearchOptionsSelector = (state) =>
  state.grid.prevSearchOptions
export const getGridSearchOptionsSelector = (state) => state.grid.searchOptions
export const gridTotalCountSelector = (state) => state.grid.totalCount

export const gridSelector = createSelector(
  gridStateSelector,
  tableSettingsSelector,
  (grid, tableSettings) => {
    const {
      data,
      searchOptions,
      marketPlaces,
      productConditions,
      totalCount,
      loading,
      queryParams,
      loadingParams,
      selectAll,
      selectedTotalCount,
    } = grid
    const {
      settings: { productsTableSettings: { settings } = {} },
      footerBounds,
      visible,
    } = tableSettings

    const selectedCount = selectedTotalCount
      ? totalCount
      : data.filter(({ selected }) => selected).length

    return {
      data,
      searchOptions,
      columns: settings,
      tableSettingsVisible: visible,
      filtersData: {
        marketPlaces: (marketPlaces || []).reduce((acc, marketPlace) => {
          if (
            !acc.find(({ id }) => id === marketPlace.id) &&
            marketPlace.active === 1
          ) {
            acc = [...acc, marketPlace]
          }

          return acc
        }, []),
        productConditions: getObjectEntries(productConditions)
          .map(([key, value]) => ({ id: +key, title: value }))
          .sort(({ id: aId, title: valueA }, { id: bId, title: valueB }) => {
            if (valueA === "New") {
              return -1
            }

            if (valueB === "New") {
              return 1
            }

            return aId - bId
          }),
      },
      totalCount: totalCount,
      loading: loading,
      selectedCount,
      queryParams,
      loadingParams,
      selectedAll: selectAll,
      selectedTotalCount,
      footerBounds,
    }
  },
)

export const selectFiltersOptions = createSelector(
  gridSelector,
  basAmazonCustomerAccountsSelector,
  productTagsSelector,
  ({ filtersData }, connectedAmazonCustomerAccounts, tags) => {
    const yesNo = [
      {
        value: "1",
        label: l("Yes"),
      },
      {
        value: "0",
        label: l("No"),
      },
    ]
    const na = {
      value: "na",
      label: l("N/A"),
    }

    return {
      stock_type: getObjectEntries(STOCK_TYPES).map(([_, value]) => ({
        value,
        label: l(value),
      })),
      isBuyBox: [...yesNo, na],
      strategy: [
        ...getObjectEntries(strategiesTypes).map(
          ([_, [strategyKey, strategyTitle]]) => ({
            value: strategyKey,
            label: l(strategyTitle),
          }),
        ),
        na,
      ],
      use_strategy_logger: [...yesNo],
      optimization_active: [...yesNo],
      isPrime: [...yesNo],
      isNationalPrime: [...yesNo],
      competitivePriceThreshold: [...yesNo],
      isFeaturedMerchant: [...yesNo, na],
      profitType: getObjectEntries(profitTypes).map(
        ([profitKey, profitTitle]) => ({
          value: profitKey,
          label: l(profitTitle),
        }),
      ),
      fbaProgram: getObjectKeys(FBA_PROGRAM_TYPES).map((key) => ({
        label: l(FBA_PROGRAM_TYPES[key]),
        value: key,
      })),
      isSnL: [...yesNo],
      is_enabled_sync_with_repricer: [...yesNo],
      condition: filtersData.productConditions
        ? filtersData.productConditions.map(({ id: value, title: label }) => ({
            value: `${value}`,
            label: l(label),
          }))
        : [],
      marketplace_id: filtersData.marketPlaces
        ? filtersData.marketPlaces.map(({ id: value, title }) => ({
            value,
            label: title,
          }))
        : [],
      seller_id: checkIsArray(connectedAmazonCustomerAccounts)
        ? connectedAmazonCustomerAccounts.map((account) => {
            return {
              value: account.sellerId,
              label: `${account.customerAccount.title} (${account.sellerId})`,
            }
          })
        : [],
      tag_id: [
        {
          value: 0,
          label: l("No tag"),
        },
        ...tags?.map((tag) => ({
          value: tag.id,
          label: tag.title,
          color: tag.color,
          withMarker: true,
        })),
      ],
    }
  },
)

export const productsForFilterItemsSelector = createSelector(
  productsForFilterSelector,
  gridStateSelector,
  amazonCustomerAccountsSelector,
  (products, { marketPlaces }, amazonCustomerAccounts) =>
    products.map((product) => ({
      ...product,
      amazonCustomerAccounts,
      marketplace: marketPlaces
        ? marketPlaces.find(({ id }) => id === product.marketplace_id)
        : undefined,
    })),
)

export const getProductByIdSelector = createSelector(
  gridStateSelector,
  ({ data: products, editFieldForm }) => {
    const foundedProduct = products?.find(({ id }) => id === editFieldForm.id)

    if (!foundedProduct) {
      return {}
    }

    const {
      id: productId,
      title,
      sku,
      asin,
      seller_id: sellerId,
      is_enabled_sync_with_repricer: isEnabledSyncWithRepricer,
      buying_price: buyingPrice,
      other_fees: otherFees,
      shipping_cost: shippingCost,
      vat,
      currency_code: currencyCode,
      repricer_id: repricerId,
    } = foundedProduct

    return {
      productId,
      isEnabledSyncWithRepricer,
      title,
      sku,
      asin,
      sellerId,
      buyingPrice,
      otherFees,
      shippingCost,
      vat,
      currencyCode,
      repricerId,
    }
  },
)

export const gridSearchOptionsSelector = createSelector(
  gridSelector,
  ({ searchOptions }) => searchOptions || {},
)

export const productConditionsSelector = createSelector(
  gridSelector,
  ({ filtersData: { productConditions } }) => {
    return checkIsArray(productConditions)
      ? productConditions.map(({ id: value, title: label }) => ({
          id: value,
          value: `${value}`,
          title: l(label),
          label: l(label),
        }))
      : []
  },
)

export const getSelectedItemsSelector = createSelector(
  gridSelector,
  ({ data }) => {
    return data?.filter((item) => item?.selected) || []
  },
)
