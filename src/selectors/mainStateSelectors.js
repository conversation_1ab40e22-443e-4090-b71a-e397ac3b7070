import { createSelector } from "reselect"

import getMainApp from "components/MainAppProvider"

import { checkIsArray } from "utils/arrayHelpers"

import { DEFAULT_CURRENCY_CODE } from "constants/currencies"

const getMainState = (state) => {
  if (state !== undefined) {
    return state.mainState || {}
  }

  const { getState } = getMainApp()

  return getState !== undefined ? getState() : {}
}

const initialState = {
  router: { location: {} },
  customer: { customer: {} },
}

export const mainStateSelector = (state) => getMainState(state) || initialState
export const authSelector = (state) => getMainState(state).auth || {}
export const routerSelector = (state) =>
  getMainState(state).router || initialState.router
export const customerSelector = (state) =>
  getMainState(state).customer || initialState.customer
export const customerHasConnectedAmazonAdAccountsSelector = (state) =>
  getMainState(state).customer?.customer?.hasConnectedAmazonAdAccounts || false
export const currentUserSelector = (state) =>
  getMainState(state).staff?.currentUser || {}
export const userIdSelector = (state) =>
  getMainState(state)?.staff?.currentUser?.id
export const staffCurrentUserRoleSelector = (state) =>
  getMainState(state).staff?.currentUser?.user?.role || {}
export const staffCurrentUserRolSideSelector = (state) =>
  getMainState(state).staff?.currentUser?.user?.roleSide
export const currenciesSelector = (state) =>
  getMainState(state).currencies || {}
export const translationsSelector = (state) =>
  getMainState(state).translations || {}
export const timezoneSelector = (state) => getMainState(state).timezone || {}
export const countriesSelector = (state) => getMainState(state).countries || {}
export const countriesOptionsSelector = (state) =>
  getMainState(state)?.countries?.countriesOptions || []
export const languagesSelector = (state) => getMainState(state).languages || {}
export const languageSelector = (state) =>
  getMainState(state)?.translations?.locale
export const viewSelector = (state) => getMainState(state).view || {}
export const viewModeSelector = (state) => getMainState(state).viewMode || {}
export const settingsSelector = (state) => getMainState(state).settings || {}
export const amazonMarketplacesSelector = (state) =>
  getMainState(state).amazonMarketplaces || []
export const amazonCustomerAccountsSelector = (state) =>
  getMainState(state).amazonCustomerAccounts || []
export const amazonCustomerAccountStockSelector = (state) =>
  getMainState(state).amazonCustomerAccountStock || {}
export const amazonCustomerAccountMarketplaceSelector = (state) =>
  getMainState(state).amazonCustomerAccountMarketplace || []
export const amazonAdsAccountsSelector = (state) =>
  getMainState(state)?.amazonAdsAccounts?.amazonAdsAccounts || []
export const navigationHasErrorSelector = (state) =>
  getMainState(state).navigation.hasError || false

export const defaultCurrencyByHomeMarketplaceSelector = createSelector(
  amazonCustomerAccountMarketplaceSelector,
  amazonCustomerAccountsSelector,
  ({ marketplaces }, { amazonCustomerAccounts }) => {
    const home_marketplace_id = amazonCustomerAccounts?.[0]?.home_marketplace_id

    return (
      marketplaces.find(
        ({ marketplace_id }) => marketplace_id === home_marketplace_id,
      )?.amazonMarketplace.currency || DEFAULT_CURRENCY_CODE
    )
  },
)

export const customerIdSelector = createSelector(
  customerSelector,
  (customer) => {
    return customer?.customer?.id
  },
)

export const basCustomerPlanSelector = (state) =>
  getMainState(state)?.basCustomerPlan?.customer

export const basPlanLoadingStatusSelector = (state) =>
  !!getMainState(state)?.basCustomerPlan?.isPlanLoading

export const basPlanFinishDateSelector = (state) => {
  const basCustomerPlan = basCustomerPlanSelector(state)

  return (
    basCustomerPlan?.data?.[basCustomerPlan.data.length - 1]?.date_finish || ""
  )
}

export const previousSubscriptionAmazonAccountsSelector = createSelector(
  basCustomerPlanSelector,
  (basCustomerPlan) => {
    const lastPlan = checkIsArray(basCustomerPlan?.data)
      ? basCustomerPlan?.data?.findLast?.(
          ({ active, amazonCustomerAccounts }) =>
            active === 0 && amazonCustomerAccounts?.length > 0,
        ) || {}
      : {}

    return lastPlan?.amazonCustomerAccounts || []
  },
)

export const isRepricerSubscriptionActiveSelector = (state) => {
  const { customer } = customerSelector(state)

  return customer?.use_repricer_module === 1
}

export const isFreemiumBasModelActiveSelector = (state) => {
  const { customer } = customerSelector(state)

  // freemium_bas_enabled - This key is temporary and will be removed later.
  return !!customer?.use_lost_module && !!customer?.freemium_bas_enabled
}

export const isBasModuleStartedSelector = (state) => {
  const { customer } = customerSelector(state)

  return customer?.bas_module_started || 0
}

export const isBasSubscriptionExpiredSelector = (state) => {
  const { customer } = customerSelector(state)

  return customer?.use_bas_module === 0
}

export const isLoadedBasPlanForCurrentCustomerSelector = createSelector(
  basCustomerPlanSelector,
  customerIdSelector,
  (basCustomerPlan, customerId) => {
    const basPlanCustomerId = basCustomerPlan?.data?.[0]?.customer_id

    return basPlanCustomerId === customerId
  },
)

export const isDemoAccountSelector = (state) =>
  getMainState(state)?.customer?.customer?.is_demo

export const permissionsSelector = () => {
  const { selectors } = getMainApp()

  return selectors?.permissionsSelector?.() || {}
}

export const allAmazonMarketplacesSelector = createSelector(
  amazonMarketplacesSelector,
  ({ amazonMarketplaces }) => amazonMarketplaces,
)

export const activeMarketplacesSelector = createSelector(
  amazonMarketplacesSelector,
  ({ amazonMarketplaces }) =>
    amazonMarketplaces?.filter(({ active }) => !!active) || [],
)

export const amazonAccountHomeMarketplaceSelector = createSelector(
  amazonCustomerAccountMarketplaceSelector,
  amazonCustomerAccountsSelector,
  (amazonCustomerAccountMarketplace, { amazonCustomerAccounts }) =>
    amazonCustomerAccountMarketplace?.marketplaces?.find(
      ({ marketplace_id }) =>
        marketplace_id === amazonCustomerAccounts[0]?.home_marketplace_id,
    )?.amazonMarketplace || {},
)

export const basAmazonCustomerAccountsSelector = createSelector(
  amazonCustomerAccountsSelector,
  isBasSubscriptionExpiredSelector,
  previousSubscriptionAmazonAccountsSelector,
  (
    { amazonCustomerAccounts },
    isBasSubscriptionExpired,
    previousSubscriptionAmazonAccounts,
  ) => {
    const activeAccounts =
      amazonCustomerAccounts?.filter(
        ({ use_bas_module }) => !!use_bas_module,
      ) || []

    return isBasSubscriptionExpired
      ? previousSubscriptionAmazonAccounts
      : activeAccounts
  },
)

export const preservedAmazonCustomerAccountsSelector = createSelector(
  amazonCustomerAccountsSelector,
  ({ amazonCustomerAccounts }) =>
    amazonCustomerAccounts?.filter(({ deleted }) => !deleted) || [],
)

export const getAmazonAccountBySellerIdSelector = (state, accountSellerId) =>
  amazonCustomerAccountsSelector(state)?.amazonCustomerAccounts?.find(
    ({ sellerId }) => sellerId === accountSellerId,
  ) || {}

export const getAmazonMarketplaceByIdSelector = (state, marketplaceId) =>
  amazonMarketplacesSelector(state).amazonMarketplaces?.find(
    ({ id }) => id === marketplaceId,
  ) || {}
