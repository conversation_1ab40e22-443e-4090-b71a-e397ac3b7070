import { createSelector } from "reselect"

import { RootState } from "types/store/store"

export const orderItemsTableNewSelector = (state: RootState) =>
  state.orderItemsTableNew

export const orderItemsTableNewTableSettingsSelector = createSelector(
  orderItemsTableNewSelector,
  (state) => state.tableSettings,
)

export const orderItemsTableNewTableStatesSelector = createSelector(
  orderItemsTableNewSelector,
  ({ orderItems }) => ({
    data: Array.isArray(orderItems?.data?.data) ? orderItems.data.data : [],
    totalCount: orderItems?.data?.totalCount || 0,
    isLoading: orderItems?.status === "PENDING",
  }),
)

export const orderItemsTableNewOrderStatusesSelector = createSelector(
  orderItemsTableNewSelector,
  (state) => state.orderStatuses?.data,
)
