import { createSelector } from "reselect"

import { userGroupedMarketplaceSelector } from "selectors/marketplaceSelectors"

import { ASYNC_STATUSES } from "constants/async"

import type { ProductAggregatedSalesInfoState } from "types/store/ProductAggregatedSalesInfo"
import type { RootState } from "types/store/store"
import type { TableSettings } from "types/TableSettings"

export const productAggregatedSalesInfoSelector = (state: RootState) => {
  return state.productAggregatedSalesInfo
}

export const productAggregatedSalesInfoTableSettingsSelector = createSelector(
  [productAggregatedSalesInfoSelector, userGroupedMarketplaceSelector],
  (state: ProductAggregatedSalesInfoState, { basAccounts }) => {
    const { tableSettings } = state

    const settings = tableSettings.settings.map((setting) => {
      if (setting.name === "seller_id") {
        return {
          ...setting,
          value: basAccounts.length > 1,
        }
      }

      return setting
    })

    return {
      ...tableSettings,
      settings,
    } as TableSettings
  },
)

export const productAggregatedSalesInfoTableStatesSelector = createSelector(
  productAggregatedSalesInfoSelector,
  (state: ProductAggregatedSalesInfoState) => {
    const isLoading =
      state.productAggregatedSalesInfo?.status === ASYNC_STATUSES.PENDING

    if (!state.productAggregatedSalesInfo?.data) {
      return {
        data: [],
        totalCount: 0,
        isLoading,
      }
    }

    const { data, totalCount } = state.productAggregatedSalesInfo.data || {}

    const dataWithId = data.map((item, index) => {
      const id = `${item.product_id}-${item.product_asin}-${index}`

      return {
        id,
        ...item,
      }
    })

    return {
      data: dataWithId,
      totalCount: totalCount,
      isLoading,
    }
  },
)
