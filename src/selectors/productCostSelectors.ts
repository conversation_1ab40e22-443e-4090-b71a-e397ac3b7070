import type { Tab } from "@develop/fe-library"
import { getObjectKeys } from "@develop/fe-library/dist/utils"
import { createSelector } from "reselect"

import {
  amazonMarketplacesSelector,
  currenciesSelector,
  isBasSubscriptionExpiredSelector,
  languageSelector,
  translationsSelector,
} from "selectors/mainStateSelectors"
import { orderAmountCostsSelector } from "selectors/ordersSelectors"
import { userTimezoneSelector } from "selectors/timezoneSelectors"

import { checkIsArray } from "utils/arrayHelpers"

import { STOCK_TYPES } from "constants/formConstants"
import {
  FORM_TYPES,
  PERIOD_SOURCES,
  SHIPPING_COST_TAB_KEYS,
} from "constants/productCost"

import { Product, ProductCostPeriod } from "../types/Models"
import { getImageUrlFromProduct } from "../utils/getImageUrlFromProduct"

import type { ProductCostFormType } from "types/ProductCostFormType"
import type {
  MarketplacesState,
  ProductCostState,
} from "types/store/ProductCost"

// BASE SELECTORS
const productCostSelector = (state): ProductCostState => {
  return state.productCost as ProductCostState
}

export const formTypeSelector = createSelector(
  productCostSelector,
  (productCost): ProductCostFormType => {
    return productCost.formType || FORM_TYPES.buying_price
  },
)

export const productSelector = createSelector(
  productCostSelector,
  (productCost) => {
    return productCost.product
  },
)

export const orderSelector = createSelector(
  productCostSelector,
  (productCost) => {
    return productCost.order
  },
)

export const isFormChangedSelector = createSelector(
  productCostSelector,
  (productCost) => {
    return productCost.isFormChanged
  },
)

export const isCreatePeriodModalVisibleSelector = createSelector(
  productCostSelector,
  (productCost) => {
    return productCost.isCreatePeriodModalVisible
  },
)

export const isDisclaimerModalVisibleSelector = createSelector(
  productCostSelector,
  (productCost) => {
    return productCost.isDisclaimerModalVisible
  },
)

export const isTransferCostsModalVisibleSelector = createSelector(
  productCostSelector,
  (productCost) => {
    return productCost.isTransferCostsModalVisible
  },
)

export const homeMarketplaceIdSelector = createSelector(
  productCostSelector,
  (productCost) => {
    return productCost.homeMarketplaceId
  },
)

export const selectedMarketplaceIdSelector = createSelector(
  productCostSelector,
  (productCost) => {
    return productCost.selectedMarketplaceId
  },
)

export const selectedShippingCostTabSelector = createSelector(
  productCostSelector,
  (productCost) => {
    return productCost.selectedShippingCostTab
  },
)

export const categoriesSelector = createSelector(
  productCostSelector,
  (productCost) => {
    return productCost.categories.categories
  },
)

const marketplacesObjectSelector = (state): MarketplacesState => {
  return state.productCost.marketplaces
}

export const isMarketplacesListLoadingSelector = createSelector(
  marketplacesObjectSelector,
  (marketplacesObject) => {
    return marketplacesObject.isLoading
  },
)

const marketplacesSelector = createSelector(
  marketplacesObjectSelector,
  (marketplacesObject) => {
    return marketplacesObject.marketplaces
  },
)

export const selectedMarketplacePeriodsObjectSelector = createSelector(
  productCostSelector,
  (productCost) => {
    return productCost.selectedMarketplacePeriods
  },
)

export const selectedPeriodIdSelector = createSelector(
  productCostSelector,
  (productCost) => {
    return productCost.selectedPeriodId
  },
)

export const isEnableSyncWithRepricerLoadingSelector = createSelector(
  productCostSelector,
  (productCost) => {
    return productCost.isEnableSyncWithRepricerLoading
  },
)

// COMBINED SELECTORS
export const productCostDrawerStatesSelector = createSelector(
  productCostSelector,
  ({
    formType,
    product,
    order,
    isFormChanged,
    isCreatePeriodModalVisible,
    isDisclaimerModalVisible,
    isTransferCostsModalVisible,
    selectedShippingCostTab,
  }) => {
    const isShippingCost: boolean = formType === FORM_TYPES.shipping_cost

    const hasShippingCostTabs: boolean = isShippingCost && !!order

    const isProductShippingCost: boolean =
      isShippingCost &&
      (!order || selectedShippingCostTab === SHIPPING_COST_TAB_KEYS.product)

    const isPeriods: boolean =
      formType === FORM_TYPES.buying_price ||
      formType === FORM_TYPES.other_fees ||
      formType === FORM_TYPES.vat ||
      isProductShippingCost

    const isSynchronizeWithRepricer: boolean =
      formType === FORM_TYPES.synchronize_with_repricer

    const isShippingCostTabEnabled: boolean =
      product?.stock_type === STOCK_TYPES.fbm ||
      !!product?.is_multiple_stock_type

    return {
      formType,
      isPeriods,
      isSynchronizeWithRepricer,
      hasShippingCostTabs,
      isFormChanged,
      isCreatePeriodModalVisible,
      isDisclaimerModalVisible,
      isTransferCostsModalVisible,
      isShippingCostTabEnabled,
    }
  },
)

export const marketplacesTabsStatesSelector = createSelector(
  formTypeSelector,
  homeMarketplaceIdSelector,
  selectedMarketplaceIdSelector,
  isMarketplacesListLoadingSelector,
  marketplacesSelector,
  amazonMarketplacesSelector,
  isFormChangedSelector,
  (
    formType,
    homeMarketplaceId,
    selectedMarketplaceId,
    isLoading,
    marketplaces,
    { amazonMarketplaces },
    isFormChanged,
  ) => {
    if (isLoading) {
      const tabsLoading = Array(3)
        .fill(0)
        .map((_, i) => {
          return {
            key: `loading-${i}`,
            contents: `loading-${i}`,
          }
        })

      return {
        formType,
        homeMarketplaceId,
        selectedMarketplaceId,
        isLoading,
        isFormChanged,
        items: tabsLoading,
        marketplaceFlags: {},
        selectedTabIndex: null,
      }
    }

    const marketplaceFlags: Record<string, string> = {}
    let selectedTabIndex

    const items = getObjectKeys(marketplaces).reduce((acc, key, index) => {
      const { marketplace_id, stock_type, is_multiple_stock_type } =
        marketplaces[key]

      const shouldInclude: boolean =
        formType === FORM_TYPES.shipping_cost
          ? stock_type === STOCK_TYPES.fbm || is_multiple_stock_type
          : true

      if (!shouldInclude) {
        return acc
      }

      const country = amazonMarketplaces?.find(
        ({ id }) => id === marketplace_id,
      )?.country

      const item: Tab = {
        key: marketplace_id,
        label: country,
        contents: marketplace_id,
      }

      marketplaceFlags[marketplace_id] = country

      if (marketplace_id === selectedMarketplaceId) {
        selectedTabIndex = index
      }

      const isHomeMarketplace: boolean = marketplace_id === homeMarketplaceId

      if (isHomeMarketplace) {
        acc.unshift(item)

        return acc
      }

      acc.push(item)

      return acc
    }, [] as Array<Tab>)

    return {
      formType,
      homeMarketplaceId,
      selectedMarketplaceId,
      isLoading,
      isFormChanged,
      items,
      marketplaceFlags,
      selectedTabIndex,
    }
  },
)

export const selectedMarketplaceSelector = createSelector(
  selectedMarketplaceIdSelector,
  marketplacesSelector,
  (selectedMarketplaceId, marketplaces) => {
    const hasSelectedMarketplace =
      !!selectedMarketplaceId && !!marketplaces[selectedMarketplaceId]

    if (!hasSelectedMarketplace) {
      return null
    }

    return marketplaces[selectedMarketplaceId]
  },
)

export const selectedPeriodSelector = createSelector(
  selectedMarketplacePeriodsObjectSelector,
  selectedPeriodIdSelector,
  (selectedMarketplacePeriods, periodId): ProductCostPeriod | null => {
    const { periods } = selectedMarketplacePeriods

    return periods.find((period) => period.id === periodId) || null
  },
)

export const currentPeriodSelector = createSelector(
  selectedMarketplacePeriodsObjectSelector,
  (selectedMarketplacePeriods): ProductCostPeriod | null => {
    const { periods } = selectedMarketplacePeriods

    return periods.find((period) => period.is_current) || null
  },
)

export const transferCostsModalStatesSelector = createSelector(
  formTypeSelector,
  selectedMarketplaceIdSelector,
  marketplacesSelector,
  amazonMarketplacesSelector,
  (formType, selectedMarketplaceId, marketplaces, { amazonMarketplaces }) => {
    const marketplacesList = getObjectKeys(marketplaces)
      .filter((marketplaceId) => marketplaceId !== selectedMarketplaceId)
      .map((marketplaceId) => {
        return amazonMarketplaces.find(({ id }) => id === marketplaceId)
      })

    return {
      formType,
      marketplaces: marketplacesList,
    }
  },
)

export const periodsStatesSelector = createSelector(
  formTypeSelector,
  selectedMarketplaceIdSelector,
  marketplacesSelector,
  isMarketplacesListLoadingSelector,
  selectedMarketplacePeriodsObjectSelector,
  selectedPeriodIdSelector,
  isBasSubscriptionExpiredSelector,
  (
    formType,
    selectedMarketplaceId,
    marketplaces,
    isMarketplacesListLoading,
    periodsObject,
    selectedPeriodId,
    isBasSubscriptionExpired,
  ) => {
    const emptyReturn = {
      isProductCostsEditFormVisible: false,
      isSinglePeriod: false,
      isEmptyProductPeriods: false,
      isLoading: isMarketplacesListLoading,
      isEnabledSyncWithRepricer: false,
      periods: [],
      formType,
      selectedPeriodId,
      currencyCode: null,
      productCostValue: null,
      isBasSubscriptionExpired,
    }

    // String is used below instead of constant because of typescript error
    if (!selectedMarketplaceId || formType === "synchronize_with_repricer") {
      return emptyReturn
    }

    const selectedMarketplace: Product = marketplaces[selectedMarketplaceId]

    if (!selectedMarketplace || !periodsObject) {
      return emptyReturn
    }

    const {
      currency_code,
      is_enabled_sync_with_repricer,
      [formType]: productCostValue,
    } = selectedMarketplace

    const { isLoading: isPeriodsListLoading, periods } = periodsObject

    const currentPeriod: ProductCostPeriod | null =
      periods.find((period) => period.is_current) || null

    const isProductCostsEditFormVisible: boolean =
      !!selectedPeriodId && formType !== FORM_TYPES.vat

    const isSinglePeriod: boolean = periods?.length <= 1
    const hasProductCostValueAndIsEnabledSyncWithRepricer: boolean = !!(
      productCostValue && is_enabled_sync_with_repricer
    )
    const isCurrentPeriodWithRepricerSource: boolean =
      currentPeriod?.source === PERIOD_SOURCES.repricer

    const isEmptyProductPeriods: boolean =
      !checkIsArray(periods) ||
      (hasProductCostValueAndIsEnabledSyncWithRepricer &&
        isSinglePeriod &&
        isCurrentPeriodWithRepricerSource)

    const isLoading: boolean = isMarketplacesListLoading || isPeriodsListLoading

    return {
      isProductCostsEditFormVisible,
      isSinglePeriod,
      isEmptyProductPeriods,
      isLoading,
      isEnabledSyncWithRepricer: is_enabled_sync_with_repricer,
      periods: periods || [],
      formType,
      selectedPeriodId,
      currencyCode: currency_code,
      productCostValue,
      isBasSubscriptionExpired,
    }
  },
)

export const repricerConnectAlertStatesSelector = createSelector(
  formTypeSelector,
  selectedMarketplaceIdSelector,
  marketplacesSelector,
  selectedMarketplacePeriodsObjectSelector,
  (
    formType,
    selectedMarketplaceId,
    marketplaces,
    selectedMarketplacePeriodsObject,
  ) => {
    const selectedMarketplace: Product | null = selectedMarketplaceId
      ? marketplaces[selectedMarketplaceId]
      : null

    // String is used below instead of constant because of typescript error
    if (!selectedMarketplace || formType === "synchronize_with_repricer") {
      return {
        formType,
        id: null,
        isEnabledSyncWithRepricer: false,
        currencyCode: null,
        repricerId: null,
        repricerIsDeleted: false,
        productCostValue: null,
        currentPeriodSource: null,
        isProductInRepricer: false,
      }
    }

    const {
      id,
      is_enabled_sync_with_repricer,
      currency_code,
      repricer_id,
      repricer_is_deleted,
      [formType]: productCostValue,
    } = selectedMarketplace

    const { periods } = selectedMarketplacePeriodsObject

    const currentPeriod: ProductCostPeriod | null =
      periods.find((period) => period.is_current) || null

    return {
      formType,
      id,
      isEnabledSyncWithRepricer: is_enabled_sync_with_repricer,
      currencyCode: currency_code,
      repricerId: repricer_id,
      repricerIsDeleted: repricer_is_deleted,
      productCostValue,
      currentPeriodSource: currentPeriod?.source || null,
      isProductInRepricer: repricer_id !== null && repricer_is_deleted !== null,
    }
  },
)

export const detailsStatesSelector = createSelector(
  formTypeSelector,
  productSelector,
  selectedMarketplaceIdSelector,
  marketplacesSelector,
  isMarketplacesListLoadingSelector,
  isBasSubscriptionExpiredSelector,
  amazonMarketplacesSelector,
  (
    formType,
    product,
    selectedMarketplaceId,
    marketplaces,
    isMarketplacesListLoading,
    isBasSubscriptionExpired,
    { amazonMarketplaces },
  ) => {
    const selectedMarketplace: Product | null = selectedMarketplaceId
      ? marketplaces[selectedMarketplaceId]
      : null

    // Can transfer costs only if there are more than one marketplace
    const canTransferCosts: boolean =
      formType !== FORM_TYPES.synchronize_with_repricer &&
      getObjectKeys(marketplaces).length > 1

    if (!selectedMarketplace) {
      return {
        title: "",
        asin: "",
        sku: "",
        stock_type: "",
        imageUrl: "",
        isBasSubscriptionExpired,
        canTransferCosts,
        sales_channel: null,
        isLoading: false,
      }
    }

    const { title, asin, sku, stock_type } = selectedMarketplace

    const amazonMarketplace = amazonMarketplaces?.find(
      ({ id }) => id === selectedMarketplaceId,
    )

    return {
      title,
      asin,
      sku,
      stock_type,
      imageUrl: getImageUrlFromProduct(product),
      isBasSubscriptionExpired,
      canTransferCosts,
      sales_channel: amazonMarketplace?.sales_channel,
      isLoading: isMarketplacesListLoading,
    }
  },
)

export const emptyPeriodsStatesSelector = createSelector(
  formTypeSelector,
  selectedMarketplaceIdSelector,
  marketplacesSelector,
  isBasSubscriptionExpiredSelector,
  (formType, selectedMarketplaceId, marketplaces, isBasSubscriptionExpired) => {
    const selectedMarketplace: Product | null = selectedMarketplaceId
      ? marketplaces[selectedMarketplaceId]
      : null

    // String is used below instead of constant because of typescript error
    if (!selectedMarketplace || formType === "synchronize_with_repricer") {
      return {
        formType,
        marketplace_id: null,
        sku: null,
        seller_id: null,
        hasSyncedRepricerWithValue: false,
        isBasSubscriptionExpired,
      }
    }

    const {
      marketplace_id,
      sku,
      seller_id,
      is_enabled_sync_with_repricer,
      [formType]: productCostValue,
    } = selectedMarketplace

    const hasSyncedRepricerWithValue = !!(
      productCostValue && is_enabled_sync_with_repricer
    )

    return {
      formType,
      marketplace_id,
      sku,
      seller_id,
      hasSyncedRepricerWithValue,
      isBasSubscriptionExpired,
    }
  },
)

export const createPeriodModalStatesSelector = createSelector(
  formTypeSelector,
  selectedMarketplaceIdSelector,
  marketplacesSelector,
  userTimezoneSelector,
  languageSelector,
  translationsSelector,
  (
    formType,
    selectedMarketplaceId,
    marketplaces,
    userTimezone,
    language,
    { locale },
  ) => {
    const selectedMarketplace: Product | null = selectedMarketplaceId
      ? marketplaces[selectedMarketplaceId]
      : null

    // String is used below instead of constant because of typescript error
    if (!selectedMarketplace || formType === "synchronize_with_repricer") {
      return {
        formType,
        marketplace_id: null,
        sku: null,
        seller_id: null,
        currency_code: null,
        productCostValue: null,
        userTimezone,
        language,
        locale,
      }
    }

    const {
      marketplace_id,
      sku,
      seller_id,
      currency_code,
      [formType]: productCostValue,
    } = selectedMarketplace

    return {
      formType,
      marketplace_id,
      sku,
      seller_id,
      currency_code,
      productCostValue,
      userTimezone,
      language,
      locale,
    }
  },
)

export const editOrderCostStatesSelector = createSelector(
  productSelector,
  orderSelector,
  orderAmountCostsSelector,
  (product, order, orderAmountCosts) => {
    const { order_id } = order || {}

    return {
      order_id,
      imageUrl: getImageUrlFromProduct(product),
      orderAmountCosts,
    }
  },
)

export const editCostItemFormStatesSelector = createSelector(
  formTypeSelector,
  selectedMarketplacePeriodsObjectSelector,
  selectedPeriodIdSelector,
  selectedMarketplaceIdSelector,
  marketplacesSelector,
  categoriesSelector,
  isFormChangedSelector,
  currenciesSelector,
  (
    formType,
    { periods },
    selectedPeriodId,
    selectedMarketplaceId,
    marketplaces,
    categories,
    isFormChanged,
    { currencies },
  ) => {
    const { id, amount_total, date_start, date_end } = (periods.find(
      (period) => period.id === selectedPeriodId,
    ) || {}) as ProductCostPeriod

    const { currency_code } = (
      selectedMarketplaceId ? marketplaces[selectedMarketplaceId] : {}
    ) as Product

    return {
      formType,
      id,
      amount_total,
      date_start,
      date_end,
      currency_code,
      categories,
      isFormChanged,
      currencies,
    }
  },
)

export const orderDetailsStatesSelector = createSelector(
  productSelector,
  orderSelector,
  isBasSubscriptionExpiredSelector,
  amazonMarketplacesSelector,
  (product, order, isBasSubscriptionExpired, { amazonMarketplaces }) => {
    const {
      product_title,
      product_asin,
      seller_sku,
      order_id,
      marketplace_id,
      amazonCustomerAccountName,
      product_stock_type,
    } = order || {}

    const amazonMarketplace = amazonMarketplaces?.find(
      ({ id }) => id === marketplace_id,
    )

    return {
      imageUrl: getImageUrlFromProduct(product),
      product_title,
      product_asin,
      seller_sku,
      order_id,
      amazonCustomerAccountName,
      product_stock_type,
      sales_channel: amazonMarketplace?.sales_channel,
      isBasSubscriptionExpired,
    }
  },
)
