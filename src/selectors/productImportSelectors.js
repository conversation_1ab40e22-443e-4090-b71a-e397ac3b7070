import { createSelector } from "reselect"

import { convertToLocalDateTime } from "utils/dateConverter"
import l from "utils/intl"
import { translateOptions } from "utils/translateOptions"

import { LOAD_TD_KEY } from "constants/grid"
import {
  FILE_HANDLER_NAME_TITLES,
  FILTER_TYPES_MAP,
  OPTIONS_FILE_HANDLER_NAMES,
  STATUS_NAMES,
  STATUS_TYPES,
} from "constants/productImport"

const productImportState = (state) => state.productImport
const productImportsSelector = (state) => state.productImport.productImports
const allProductImportsSelector = (state) =>
  state.productImport.allProductImports

const getType = ({ type }) => l(FILTER_TYPES_MAP?.[type]?.label)

export const isProductImportDataLoadingSelector = (state) =>
  state.productImport.isProductImportDataLoading

export const productImportSelector = createSelector(
  productImportState,
  ({
    currentProductImport,
    errorsModalVisible,
    searchOptions,
    totalCount,
    uploadError,
    uploadModalVisible,
    downloadTemplateError,
    isDownloadTemplateModalVisible,
  }) => ({
    errorsModalVisible,
    productImportErrors: currentProductImport?.errors || [],
    searchOptions,
    totalCount,
    uploadError,
    uploadModalVisible,
    downloadTemplateError,
    isDownloadTemplateModalVisible,
  }),
)

export const productImportItemsSelector = createSelector(
  productImportsSelector,
  allProductImportsSelector,
  (productImports, allProductImports) =>
    productImports.map((productImport) => {
      productImport =
        allProductImports.find(({ id }) => productImport.id === id) ||
        productImport

      const isStatusLoading =
        productImport.status === STATUS_TYPES.new ||
        productImport.status === STATUS_TYPES.inProgress

      const result = {
        ...productImport,
        statusLabel:
          productImport.status && STATUS_NAMES[productImport.status]
            ? l(STATUS_NAMES[productImport.status])
            : "",
        handlerName: l(FILE_HANDLER_NAME_TITLES[productImport.handler_name]),
        startDateLabel:
          productImport.started_at &&
          convertToLocalDateTime(productImport.started_at, true),
        endDateLabel:
          productImport.finished_at &&
          convertToLocalDateTime(productImport.finished_at, true),
        dateInsertedLabel:
          productImport.created_at &&
          convertToLocalDateTime(productImport.created_at, true),
        dateUpdateLabel:
          productImport.updated_at &&
          convertToLocalDateTime(productImport.updated_at, true),
        showType: getType(productImport),
      }

      if (isStatusLoading) {
        result.startDateLabel = LOAD_TD_KEY
        result.endDateLabel = LOAD_TD_KEY
        result.count_all_items = LOAD_TD_KEY
      }

      return result
    }),
)

export const filtersOptionsSelector = () => ({
  type: [
    {
      label: l(FILTER_TYPES_MAP.bulk_edit.label),
      value: FILTER_TYPES_MAP.bulk_edit.value,
    },
    {
      label: l(FILTER_TYPES_MAP.auto.label),
      value: FILTER_TYPES_MAP.auto.value,
    },
    {
      label: l(FILTER_TYPES_MAP.manual.label),
      value: FILTER_TYPES_MAP.manual.value,
    },
  ],
  status: [
    { label: l(STATUS_NAMES[STATUS_TYPES.new]), value: STATUS_TYPES.new },
    {
      label: l(STATUS_NAMES[STATUS_TYPES.inProgress]),
      value: STATUS_TYPES.inProgress,
    },
    { label: l(STATUS_NAMES[STATUS_TYPES.done]), value: STATUS_TYPES.done },
    {
      label: l(STATUS_NAMES[STATUS_TYPES.noItems]),
      value: STATUS_TYPES.noItems,
    },
  ],
  handler_name: translateOptions(OPTIONS_FILE_HANDLER_NAMES),
})
