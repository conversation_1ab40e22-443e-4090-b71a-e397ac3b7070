import { createSelector } from "reselect"

import { ProductTagsState } from "reducers/productTagsReducers"

import { checkIsArray } from "utils/arrayHelpers"

import { ASYNC_STATUSES } from "constants/async"

import { ProductTagOption } from "types"

const productTagStateSelector = (state) => state.productTags as ProductTagsState

export const productTagsSelector = createSelector(
  productTagStateSelector,
  ({ productTags }) => {
    return checkIsArray(productTags) ? productTags : []
  },
)

export const productTagInitialValuesSelector = createSelector(
  productTagStateSelector,
  ({ initialValues }) => {
    return initialValues || {}
  },
)

export const isDeleteProductTagModalVisibleSelector = (state) => {
  return state.productTags?.isDeleteProductTagModalVisible
}

export const isUpdateProductTagModalVisibleSelector = (state) => {
  return state.productTags?.isUpdateProductTagModalVisible
}

export const isCreateProductTagModalVisibleSelector = (state) => {
  return state.productTags?.isCreateProductTagModalVisible
}

export const productTagsStatusSelector = (state) =>
  state.productTags?.productTagsStatus

export const productTagsAsOptionsSelector = createSelector(
  [productTagStateSelector, productTagsStatusSelector],
  ({ productTags }, productTagsStatus): ProductTagOption[] => {
    const isProductTagsReady: boolean =
      productTagsStatus === ASYNC_STATUSES.FULFILLED && checkIsArray(productTags)

    if (!isProductTagsReady) {
      return []
    }

    return productTags.map((tag) => ({
      ...tag,
      value: tag.id.toString(),
      label: tag.title,
      tagColor: tag.color,
    }))
  },
)
