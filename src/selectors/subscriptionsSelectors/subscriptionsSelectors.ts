import { createSelector } from "reselect"

import {
  amazonCustomerAccountsSelector,
  basCustomerPlanSelector,
  basPlanLoadingStatusSelector,
  isBasModuleStartedSelector,
  isBasSubscriptionExpiredSelector,
  isDemoAccountSelector,
  isFreemiumBasModelActiveSelector,
  isRepricerSubscriptionActiveSelector,
} from "selectors/mainStateSelectors"

import {
  AmazonCustomerAccountItem,
  AmazonCustomerAccounts,
} from "./subscriptionsSelectorsTypes"

const accountDifferenceSelector = createSelector(
  [amazonCustomerAccountsSelector],
  ({
    amazonCustomerAccounts,
  }: AmazonCustomerAccounts): AmazonCustomerAccountItem[] => {
    const accountsDifference = amazonCustomerAccounts?.filter(
      (amazonCustomerAccount) => {
        return (
          amazonCustomerAccount.use_repricer_module === 1 &&
          amazonCustomerAccount.use_bas_module === 0
        )
      },
      [],
    )

    return accountsDifference || []
  },
)

export const subscriptionsSelector = createSelector(
  [
    isBasModuleStartedSelector,
    isBasSubscriptionExpiredSelector,
    isRepricerSubscriptionActiveSelector,
    accountDifferenceSelector,
    isFreemiumBasModelActiveSelector,
    isDemoAccountSelector,
    basCustomerPlanSelector,
    basPlanLoadingStatusSelector,
  ],
  (
    isBasModuleStarted,
    isBasSubscriptionExpired,
    isRepricerSubscriptionActive,
    accountDifference,
    isFreemiumBasModelActive,
    isDemoAccount,
    basCustomerPlan,
    basPlanLoadingStatus,
  ) => {
    const isBasModuleStartedFlag: boolean = !!isBasModuleStarted
    const accountDifferenceLength: number = accountDifference.length
    const accountDifferenceList: string = accountDifference
      .map(
        (amazonCustomerAccount) => amazonCustomerAccount.customerAccount.title,
      )
      .join(", ")

    return {
      isBasModuleStarted: isBasModuleStartedFlag,
      isBasSubscriptionActive:
        !isBasSubscriptionExpired && isBasModuleStartedFlag,
      isBasSubscriptionExpired,
      isBasDemoActive: isDemoAccount,
      isRepricerSubscriptionActive,
      accountDifference,
      accountDifferenceList,
      accountDifferenceLength,
      isFreemiumBasModelActive,
      basCustomerPlan,
      basPlanLoadingStatus,
    }
  },
)
