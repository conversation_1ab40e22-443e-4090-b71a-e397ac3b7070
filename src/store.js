import { applyMiddleware, compose, createStore } from "redux"
import { composeWithDevTools } from "redux-devtools-extension"
import { persistStore } from "redux-persist"
import thunkMiddleware from "redux-thunk"

import cacheAPIMiddleware from "middleware/cacheAPIMiddleware"
import callAPIMiddleware from "middleware/callAPIMiddleware"

import createRootReducer from "reducers"

function configureStore(preloadedState) {
  const middlewares = [cacheAPIMiddleware, callAPIMiddleware, thunkMiddleware]
  const middlewareEnhancer = applyMiddleware(...middlewares)
  const enhancers = [middlewareEnhancer]
  const composedEnhancers = composeWithDevTools(compose(...enhancers))

  const store = createStore(
    createRootReducer(),
    preloadedState,
    composedEnhancers,
  )

  const persistor = persistStore(store)

  return { store, persistor }
}

export default configureStore
