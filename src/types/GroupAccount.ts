import { GROUP_ACCOUNT_TYPE } from "constants/groupAccount"

import { AmazonCustomerAccount } from "./Models"

export type GroupAccountTypeKeysType =
  typeof GROUP_ACCOUNT_TYPE[keyof typeof GROUP_ACCOUNT_TYPE]

type GroupAccountOptionEntity = {
  name: GroupAccountTypeKeysType
  title: string
  label: string
  value: GroupAccountTypeKeysType
  type: GroupAccountTypeKeysType
}

export type GroupAccountOptionType =
  | GroupAccountOptionEntity
  | (GroupAccountOptionEntity & AmazonCustomerAccount)
