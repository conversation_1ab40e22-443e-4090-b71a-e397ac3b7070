type KeyValue = {
  key: string
  value: string
}

type ProductFiltersGeneralEntity = {
  [key: string]: KeyValue
}

export type ProductFiltersEntity = {
  brands: ProductFiltersGeneralEntity[]
  manufactures: ProductFiltersGeneralEntity[]
  marketplace_ids: ProductFiltersGeneralEntity[]
  offer_types: ProductFiltersGeneralEntity[]
  product_types: ProductFiltersGeneralEntity[]
  stock_types: ProductFiltersGeneralEntity[]
}
