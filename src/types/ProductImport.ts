import { TEMPLATE_HANDLER_VALUES } from "constants/productImport"
import { STATUS_TYPES } from "constants/statuses"

export type ProductImportItemType = {
  id: number
  handler_name: string
  status: keyof typeof STATUS_TYPES
  count_parts: number
  count_all_items: number
  count_imported_items: number
  count_errors: number
  file_url: string
  language_code: string
  data_start_line_number: number
  type: string
  finished_at: string
  created_at: string
  updated_at: string
  started_at: string
}

type TemplateHandlerValuesKeys = keyof typeof TEMPLATE_HANDLER_VALUES
export type TemplateHandlerValues =
  typeof TEMPLATE_HANDLER_VALUES[TemplateHandlerValuesKeys]
