import { JSONRequestParam } from "./global"

export type GetProductAggregatedSalesInfosRequestParams = {
  page?: number
  sort?: string
  pageSize?: number
  date_start?: string
  date_end?: string
  id?: number
  marketplace_seller_ids?: JSONRequestParam
  currency_id?: string
  product_id?: number
  marketplace_id?: string
  seller_id?: string
  seller_sku?: string
  product_asin?: string
  product_brand?: string
  product_ean?: string
  product_upc?: string
  product_isbn?: string
  product_title?: string
  product_manufacturer?: string
  product_type?: string
  product_parent_asin?: string
  product_stock_type?: string
  product_adult?: "1" | "0"
  estimated_profit_amount?: string
  is_transaction_date_mode?: "1" | "0"
  tag_id?: string
  orders?: string
  units?: string
  revenue_amount?: string
  total_income?: string
  expenses_amount?: string
  amazon_fees?: string
  margin?: string
  roi?: string
  markup?: string
  refunds?: string
  offer_type?: string
  ppc_costs?: string
}
