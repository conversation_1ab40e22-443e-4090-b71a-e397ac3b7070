import { BooleanRequestParam, JSONRequestParam, OfferType, Period } from "types"

export type ProfitLossRequestParams = {
  customerId?: string
  dateStart: string
  dateEnd: string
  isTransactionDateMode?: BooleanRequestParam
  marketplaceSellerIds?: JSONRequestParam
  sellerId?: string
  marketplaceId?: string
  currencyId?: string
  asin?: string
  ean?: string
  isbn?: string
  upc?: string
  title?: string
  mainImage?: string
  parentAsin?: string
  brand?: string
  model?: string
  productType?: string
  stockType?: string
  manufacturer?: string
  adultProduct?: string
  offerType?: OfferType
  sellerSku?: string
  maxDepth?: number
  salesCategoryId?: string
  periodType?: Period
  tagId?: string
}
