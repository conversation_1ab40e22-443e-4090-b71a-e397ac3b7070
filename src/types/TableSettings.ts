import { ColumnSetting } from "@develop/fe-library"

import { AsyncStatus } from "types/AsyncStatus"

import { ActionAsyncDefinitionType, ActionsTypes } from "actions/types"

export type TableSettings = {
  id: number | null
  key: string
  pageSize: number
  settings: Array<ColumnSetting>
}

export type TableSettingsState = {
  tableSettings: TableSettings
  tableSettingsStatus: AsyncStatus
}

export type TableSettingsActionsAsyncTypeNames =
  | "getTableSettings"
  | "updateTableSettings"
  | "updateDefaultTableSettings"

export type TableSettingsActionTypes =
  ActionsTypes<TableSettingsActionsAsyncTypeNames>

export type TableSettingsActionDefinitions = {
  getTableSettings: ActionAsyncDefinitionType<never>
  updateTableSettings: ActionAsyncDefinitionType<TableSettings>
  updateDefaultTableSettings: ActionAsyncDefinitionType<TableSettings>
}

export type TableSettingsColumnTitles = Record<string, string>

export type TableSettingsColumnVisibility = Record<string, boolean>

export type TableSettingsTitlesAndVisibility = {
  titles: TableSettingsColumnTitles
  defaultVisibility: TableSettingsColumnVisibility
}

export type TableSettingsMap = Record<string, TableSettingsTitlesAndVisibility>
