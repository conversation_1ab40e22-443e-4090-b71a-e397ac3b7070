export type ProductFiltersUrlParams = {
  productId?: string
  productRepricerId?: string
  productSku?: string
  productSeller?: string
  productMarketplace?: string
  productASIN?: string
}

export type ProductUrlParamsKeysType = keyof ProductFiltersUrlParams

export type ProductFiltersUniqueIdsParams = {
  asin?: string
  sku?: string
  ean?: string
  upc?: string
  isbn?: string
}

export type ProductIdsUrlParamsKeysType =
  | keyof ProductFiltersUniqueIdsParams
  | keyof ProductFiltersParentAsinParams

export type ProductAndIdsUrlParamsKeysType =
  | ProductUrlParamsKeysType
  | ProductIdsUrlParamsKeysType

export type ProductFiltersBrandParams = {
  brand?: string
}

export type ProductFiltersProductTypeParams = {
  product_type?: string
}

export type ProductFiltersStockTypeUrlParams = {
  stock_type?: string
}

export type ProductFiltersManufacturerParams = {
  manufacturer?: string
}

export type ProductFiltersParentAsinParams = {
  parent_asin?: string
}

export type ProductFiltersAgeRangeParams = {
  age_range?: string
}

export type ProductFiltersAdultProductParams = {
  adult_product?: "true" | "false"
}

export type ProductFiltersGeneralParams = ProductFiltersUrlParams &
  ProductFiltersUniqueIdsParams &
  ProductFiltersBrandParams &
  ProductFiltersProductTypeParams &
  ProductFiltersStockTypeUrlParams &
  ProductFiltersManufacturerParams &
  ProductFiltersParentAsinParams &
  ProductFiltersAgeRangeParams &
  ProductFiltersAdultProductParams
