import type {
  AsyncStatus,
  <PERSON>rid<PERSON>eneral,
} from "types"
import type { AmazonOrderExtendedViewItemV1 } from "types/Models/AmazonOrderExtendedViewItem"
import type { TableSettingsState } from "types/TableSettings"

export type OrderItemsTableNewState = {
  orderItems: {
    data: GridGeneral<Array<AmazonOrderExtendedViewItemV1>>
    status: AsyncStatus
    error: any | null
  }
} & TableSettingsState
