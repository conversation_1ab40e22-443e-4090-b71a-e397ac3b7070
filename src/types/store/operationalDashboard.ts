import { OPERATIONAL_DASHBOARD_PRIORITY_STATUSES } from "selectors/operationalDashboardSelectors/constants"

// Define allowed importance values

export type Importance =
  typeof OPERATIONAL_DASHBOARD_PRIORITY_STATUSES[keyof typeof OPERATIONAL_DASHBOARD_PRIORITY_STATUSES]
// Interface for the factor object
export type Factor = {
  id: string
  title: string
  importance: Importance
}
// Interface for a single data item
export type DataCompleteness = {
  id: number
  count_all: number
  count_unfilled: number
  is_ignored: boolean
  checked_at: string // or Date if you plan to convert it
  factor: Factor
  fill_percentage: number
}

export type OperationalDashboardState = {
  dataCompleteness: DataCompleteness[]
  isDataCompletenessLoading: boolean
  isVisibleDataReassemblyModal: boolean
  isVisibleHistoricalDataLoadModal: boolean
  isVisibleReferralFeeChangesDrawer: boolean
  isVisibleFbaFulfillmentFeeChangesDrawer: boolean
  isVisibleAdjustmentToFeesDrawer: boolean
  additionalDataModal: {}
  dataReferralFeeChanges: any[]
  isDataReferralFeeChangesLoading: boolean
  totalCount: number
  dataFbaFulfillmentFeeChanges: any[]
  isDataFbaFulfillmentFeeChangesLoading: boolean
  dataAdjustmentToFees: any[]
  isDataAdjustmentToFeesLoading: boolean
  fillPercentageWidgetHealthScoreLevel: number
}

type OperationalDashboardStoreReduction = (
  state: OperationalDashboardState,
  payload,
) => OperationalDashboardState

export type OperationalDashboardStoreReductions = {
  [key: string]: OperationalDashboardStoreReduction
}
