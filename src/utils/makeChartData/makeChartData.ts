import { getObjectKeys, getObjectValues } from "@develop/fe-library/dist/utils"

import { convertToLocalDate } from "utils/dateConverter"

import { DataSeriesCategories } from "components/SalesHistoryWidget/SalesHistoryWidgetTypes"

import { MakeChartData } from "./makeChartDataTypes"

export const makeChartData: MakeChartData = ({
  data,
  returnAbsoluteValues = false,
}) => {
  return getObjectValues(data).map(
    ({
      salesCategories,
      date,
      dateStart,
      dateEnd,
      expenses,
      revenue,
      units,
    }) => {
      const convertedDate: string = convertToLocalDate(date) || date
      const convertedDateStart: string = convertToLocalDate(dateStart)
      const convertedDateEnd: string = convertToLocalDate(dateEnd)
      const dateRange: string = `${dateStart} - ${dateEnd}`

      const salesCategoriesValues = getObjectKeys<DataSeriesCategories>(
        salesCategories,
      ).reduce(
        (memo, category) => ({
          ...memo,
          [category]: returnAbsoluteValues
            ? Math.abs(salesCategories[category])
            : salesCategories[category],
        }),
        {} as DataSeriesCategories,
      )

      return {
        date: convertedDate,
        dateRange,
        dateStart: convertedDateStart,
        dateEnd: convertedDateEnd,
        expenses: returnAbsoluteValues ? Math.abs(expenses) : expenses,
        revenue: returnAbsoluteValues ? Math.abs(revenue) : revenue,
        salesCategories: salesCategoriesValues,
        units,
      }
    },
  )
}
